package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.StoreStaffTypeEnum;
import com.eastking.enums.StoreStatusEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.model.dto.StoreDto;
import com.eastking.model.dto.UserAccountSlimDto;
import com.eastking.model.dto.TaxIdentificationDto;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.RegionService;
import com.eastking.service.StoreService;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.DataConflictException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StoreServiceImpl implements StoreService {

    private static final Logger logger = LoggerFactory.getLogger(StoreServiceImpl.class);

    private final StoreRepository storeRepository;
    private final RegionService regionService;
    private final UserAccountRepository userAccountRepository;
    private final TaxIdentificationRepository taxIdentificationRepository;
    private final StoreStaffMapRepository storeStaffMapRepository;
    private final StoreTaxIdentificationMapRepository storeTaxIdentificationMapRepository;

    @Override
    @Transactional
    public StoreDto createStore(StoreDto storeDto) {
        if (storeRepository.findByStoreNameAndIsDeleted(storeDto.getStoreName(), DeleteStatusEnum.NOT_DELETED.getCode()).isPresent() ||
            (StringUtils.hasText(storeDto.getStoreCode()) && storeRepository.findByStoreCodeAndIsDeleted(storeDto.getStoreCode(), DeleteStatusEnum.NOT_DELETED.getCode()).isPresent())) {
            throw new DataConflictException("Store with the same name or code already exists.");
        }
        StoreEntity storeEntity = new StoreEntity();
        BeanUtils.copyProperties(storeDto, storeEntity, "storeId", "regionName", 
                                 "staffUserAccountIds", "substituteUserAccountIds", "taxIdentificationIds", 
                                 "staffList", "taxIdentifications", "isActive", "isDeleted", 
                                 "createBy", "createTime", "updateBy", "updateTime", "createByName", "updateByName",
                                 "erpCompanyDivisionDescription");
        
        storeEntity.setIsActive( (storeDto.getIsActive() != null && storeDto.getIsActive()) ? (short)1 : (short)0 );
        storeEntity.setErpCompanyDivision(storeDto.getErpCompanyDivision());
        storeEntity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());

        if (storeDto.getRegionId() != null) {
            RegionEntity region = regionService.findRegionEntityById(storeDto.getRegionId()); 
            storeEntity.setRegion(region);
        }
        
        StoreEntity savedStore = storeRepository.save(storeEntity);
        
        updateStoreStaff(savedStore, storeDto.getStaffUserAccountIds(), StoreStaffTypeEnum.REGULAR_STAFF.getCode()); 
        updateStoreStaff(savedStore, storeDto.getSubstituteUserAccountIds(), StoreStaffTypeEnum.SUBSTITUTE_STAFF.getCode()); 
        updateStoreTaxIdentifications(savedStore, storeDto.getTaxIdentificationIds());
        
        StoreEntity fetchedStore = storeRepository.findById(savedStore.getStoreId()).orElse(savedStore);
        return convertToDtoWithDetails(fetchedStore);
    }

    @Override
    @Transactional
    public StoreDto updateStore(UUID storeId, StoreDto storeDto) {
        StoreEntity storeEntity = storeRepository.findByStoreIdAndIsDeleted(storeId, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("Store", "ID", storeId));

        storeRepository.findByStoreNameAndIsDeleted(storeDto.getStoreName(), DeleteStatusEnum.NOT_DELETED.getCode())
            .filter(s -> !s.getStoreId().equals(storeId))
            .ifPresent(s -> { throw new DataConflictException("Another store with the same name already exists."); });
        if (StringUtils.hasText(storeDto.getStoreCode())) {
            storeRepository.findByStoreCodeAndIsDeleted(storeDto.getStoreCode(), DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(s -> !s.getStoreId().equals(storeId))
                .ifPresent(s -> { throw new DataConflictException("Another store with the same code already exists."); });
        }

        BeanUtils.copyProperties(storeDto, storeEntity, "storeId", "regionName", 
                                 "staffUserAccountIds", "substituteUserAccountIds", "taxIdentificationIds", 
                                 "staffList", "taxIdentifications", "isActive", "isDeleted",
                                 "createBy", "createTime", "updateBy", "updateTime", "createByName", "updateByName",
                                 "erpCompanyDivisionDescription");
        storeEntity.setIsActive( (storeDto.getIsActive() != null && storeDto.getIsActive()) ? (short)1 : (short)0 );
        storeEntity.setErpCompanyDivision(storeDto.getErpCompanyDivision());

        if (storeDto.getRegionId() != null) {
            if (storeEntity.getRegion() == null || !storeDto.getRegionId().equals(storeEntity.getRegion().getRegionId())) {
                RegionEntity region = regionService.findRegionEntityById(storeDto.getRegionId());
                storeEntity.setRegion(region);
            }
        } else {
            storeEntity.setRegion(null);
        }
        updateStoreStaff(storeEntity, storeDto.getStaffUserAccountIds(), StoreStaffTypeEnum.REGULAR_STAFF.getCode());
        updateStoreStaff(storeEntity, storeDto.getSubstituteUserAccountIds(), StoreStaffTypeEnum.SUBSTITUTE_STAFF.getCode());
        updateStoreTaxIdentifications(storeEntity, storeDto.getTaxIdentificationIds());
        
        StoreEntity updatedStore = storeRepository.save(storeEntity);
        StoreEntity fetchedStore = storeRepository.findById(updatedStore.getStoreId()).orElse(updatedStore);
        return convertToDtoWithDetails(fetchedStore);
    }

    private void updateStoreStaff(StoreEntity storeEntity, List<UUID> staffIds, short staffType) {
        if (staffIds == null) staffIds = Collections.emptyList();
        
        List<StoreStaffMapEntity> existingMaps = storeStaffMapRepository
            .findByStoreAndStaffTypeAndIsDeleted(storeEntity, staffType, DeleteStatusEnum.NOT_DELETED.getCode());
        
        Set<UUID> newStaffIdSet = new HashSet<>(staffIds);
        List<StoreStaffMapEntity> mapsToRemove = new ArrayList<>();
        Set<UUID> existingStaffIdSet = new HashSet<>();

        for (StoreStaffMapEntity map : existingMaps) {
            if (map.getUserAccount() == null) { 
                 mapsToRemove.add(map);
                 continue;
            }
            if (!newStaffIdSet.contains(map.getUserAccount().getUserAccountId())) {
                mapsToRemove.add(map);
            } else {
                existingStaffIdSet.add(map.getUserAccount().getUserAccountId());
            }
        }
        for (StoreStaffMapEntity mapToRemove : mapsToRemove) {
            mapToRemove.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
            storeStaffMapRepository.save(mapToRemove);
        }
        for (UUID staffId : newStaffIdSet) {
            if (!existingStaffIdSet.contains(staffId)) {
                UserAccount user = userAccountRepository.findById(staffId)
                    .orElseThrow(() -> new ResourceNotFoundException("UserAccount for staff mapping", "ID", staffId));
                StoreStaffMapEntity newMap = new StoreStaffMapEntity();
                newMap.setStore(storeEntity);
                newMap.setUserAccount(user);
                newMap.setStaffType(staffType);
                newMap.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                storeStaffMapRepository.save(newMap);
            }
        }
    }

    private void updateStoreTaxIdentifications(StoreEntity storeEntity, List<UUID> taxIdentificationEntityIds) {
        if (taxIdentificationEntityIds == null) taxIdentificationEntityIds = Collections.emptyList();
        
        List<StoreTaxIdentificationMapEntity> existingMaps = storeTaxIdentificationMapRepository
            .findByStoreAndIsDeleted(storeEntity, DeleteStatusEnum.NOT_DELETED.getCode());
        Set<UUID> newTaxIdEntitySet = new HashSet<>(taxIdentificationEntityIds);
        List<StoreTaxIdentificationMapEntity> mapsToRemove = new ArrayList<>();
        Set<UUID> existingTaxIdEntitySet = new HashSet<>();

        for(StoreTaxIdentificationMapEntity map : existingMaps) {
            if (map.getTaxIdentification() == null) {
                mapsToRemove.add(map);
                continue;
            }
            if(!newTaxIdEntitySet.contains(map.getTaxIdentification().getTaxIdentificationId())) {
                mapsToRemove.add(map);
            } else {
                existingTaxIdEntitySet.add(map.getTaxIdentification().getTaxIdentificationId());
            }
        }
        for(StoreTaxIdentificationMapEntity mapToRemove : mapsToRemove) {
            mapToRemove.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
            storeTaxIdentificationMapRepository.save(mapToRemove);
        }
        for(UUID taxIdEntityUuid : newTaxIdEntitySet) {
            if(!existingTaxIdEntitySet.contains(taxIdEntityUuid)) {
                TaxIdentificationEntity taxId = taxIdentificationRepository.findById(taxIdEntityUuid)
                    .orElseThrow(() -> new ResourceNotFoundException("TaxIdentification for mapping", "ID", taxIdEntityUuid));
                StoreTaxIdentificationMapEntity newMap = new StoreTaxIdentificationMapEntity();
                newMap.setStore(storeEntity);
                newMap.setTaxIdentification(taxId);
                newMap.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                storeTaxIdentificationMapRepository.save(newMap);
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<StoreDto> getStoreById(UUID id) {
        return storeRepository.findByStoreIdAndIsDeleted(id, DeleteStatusEnum.NOT_DELETED.getCode()).map(this::convertToDtoWithDetails);
    }
    
    private StoreDto convertToDtoWithDetails(StoreEntity entity) {
        return convertToDto(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StoreDto> getAllStores(Pageable pageable, String storeName, UUID regionId, Boolean isActive, String companyContext) {
        logger.debug("Searching stores with name: {}, regionId: {}, isActive: {}, companyContext: {}", storeName, regionId, isActive, companyContext);
        Specification<StoreEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(storeName)) {
                predicates.add(cb.like(cb.lower(root.get("storeName")), "%" + storeName.toLowerCase() + "%"));
            }
            if (regionId != null) {
                predicates.add(cb.equal(root.get("region").get("regionId"), regionId));
            }
            if (isActive != null) {
                predicates.add(cb.equal(root.get("isActive"), (isActive ? (short)1 : (short)0) ));
            }
            if (StringUtils.hasText(companyContext)) {
                if ("EASTKING".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.EASTKING.getCode()));
                } else if ("QUEYOU".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.QUEYOU.getCode()));
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Page<StoreEntity> storePage = storeRepository.findAll(spec, pageable);
        return storePage.map(this::convertToDto); 
    }

    @Override
    @Transactional
    public void deleteStore(UUID id) {
        StoreEntity store = storeRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Store", "ID", id));
        store.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        store.setIsActive((short)0); 
        storeRepository.save(store);
        logger.info("Store soft deleted with ID: {}", id);
    }

    private StoreDto convertToDto(StoreEntity entity) {
        StoreDto dto = new StoreDto();
        BeanUtils.copyProperties(entity, dto, "region", "storeStaffMaps", "storeTaxIdentificationMaps", "isActive", "erpCompanyDivisionDescription");
        dto.setIsActive(entity.getIsActive() != null && entity.getIsActive() == 1);
        dto.setErpCompanyDivisionFields(entity.getErpCompanyDivision());

        if (entity.getRegion() != null) {
            dto.setRegionId(entity.getRegion().getRegionId());
            dto.setRegionName(entity.getRegion().getRegionName());
        }
        
        if (entity.getStoreStaffMaps() != null) {
            dto.setStaffList(entity.getStoreStaffMaps().stream()
                .filter(map -> map.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode() && map.getUserAccount() != null)
                .map(map -> UserAccountSlimDto.fromEntity(map.getUserAccount()))
                .distinct()
                .collect(Collectors.toList()));
        }

        if (entity.getStoreTaxIdentificationMaps() != null) {
            dto.setTaxIdentifications(entity.getStoreTaxIdentificationMaps().stream()
                .filter(map -> map.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode() && map.getTaxIdentification() != null)
                .map(map -> {
                    TaxIdentificationEntity tax = map.getTaxIdentification();
                    return new TaxIdentificationDto(tax.getTaxIdentificationId(), tax.getTaxIdNumber(), tax.getCompanyName(), 
                                                tax.getIsDeleted(), tax.getCreateBy(), tax.getCreateTime(), tax.getUpdateBy(), tax.getUpdateTime());
                })
                .distinct()
                .collect(Collectors.toList()));
        }
        dto.setCreateBy(entity.getCreateBy());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setUpdateTime(entity.getUpdateTime());
        return dto;
    }

    private StoreDto convertToDtoSimple(StoreEntity entity) {
        StoreDto dto = new StoreDto();
        dto.setStoreId(entity.getStoreId());
        dto.setStoreName(entity.getStoreName());
        dto.setStoreCode(entity.getStoreCode());
        dto.setIsActive(entity.getIsActive() != null && entity.getIsActive() == 1); 
        dto.setErpCompanyDivisionFields(entity.getErpCompanyDivision());
        if (entity.getRegion() != null) {
            dto.setRegionId(entity.getRegion().getRegionId());
            dto.setRegionName(entity.getRegion().getRegionName());
        }
        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public List<StoreDto> getAllActiveSelectableStores(String companyContext) {
        logger.debug("Fetching all active and selectable stores for companyContext: {}", companyContext);
        Specification<StoreEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isActive"), (short) 1));
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(companyContext)) {
                if ("EASTKING".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.EASTKING.getCode()));
                } else if ("QUEYOU".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.QUEYOU.getCode()));
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Sort sort = Sort.by(Sort.Direction.ASC, "storeName");
        List<StoreEntity> stores = storeRepository.findAll(spec, sort);
        return stores.stream().map(this::convertToDtoSimple).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<StoreDto> getSelectableStoresByRegion(UUID regionId, String companyContext) {
        logger.debug("Fetching active and selectable stores for regionId: {}, companyContext: {}", regionId, companyContext);
        Specification<StoreEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isActive"), (short) 1));
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (regionId != null) {
                predicates.add(cb.equal(root.get("region").get("regionId"), regionId));
            }
            if (StringUtils.hasText(companyContext)) {
                if ("EASTKING".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.EASTKING.getCode()));
                } else if ("QUEYOU".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.QUEYOU.getCode()));
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Sort sort = Sort.by(Sort.Direction.ASC, "storeName");
        List<StoreEntity> stores = storeRepository.findAll(spec, sort);
        return stores.stream().map(this::convertToDtoSimple)
            .collect(Collectors.toList());
    }
} 