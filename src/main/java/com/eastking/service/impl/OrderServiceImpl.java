package com.eastking.service.impl;

import com.eastking.model.dto.request.*;
import com.eastking.model.dto.response.OrderDetailDto;
import com.eastking.model.dto.response.OrderItemDto;
import com.eastking.model.dto.response.OrderSummaryDto;
import com.eastking.model.dto.response.OrderRefundDto;
import com.eastking.model.dto.response.OrderItemGroupDto;
import com.eastking.model.entity.*;
import com.eastking.enums.*;
import com.eastking.repository.*;
import com.eastking.service.AuditLogService;
import com.eastking.service.OrderService;
import com.eastking.service.WarehouseStoreInventoryService;
import com.eastking.service.OrderChangeLogService;
import com.eastking.exception.BadRequestException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashSet;
import java.util.HashMap;
import java.util.Set;
import java.util.function.Consumer;
import java.util.Comparator;

import com.eastking.model.dto.OrderPaymentDto;
import com.eastking.model.dto.response.ChangeOrderResponseDto;
import com.eastking.model.entity.Warehouse;
import com.eastking.repository.WarehouseRepository;
import com.eastking.service.InventoryService;
import com.eastking.repository.PromotionRepository;
import com.eastking.repository.DispatchRepairRepository;
import com.eastking.service.DispatchRepairService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class OrderServiceImpl implements OrderService {

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final CustomerRepository customerRepository;
    private final StoreRepository storeRepository;
    private final DistributorRepository distributorRepository;
    private final UserAccountRepository userAccountRepository;
    private final ProductSettingRepository productSettingRepository;
    private final AuditLogService auditLogService;
    private final WarehouseStoreInventoryService warehouseStoreInventoryService;
    private final StoreDailyJournalRepository storeDailyJournalRepository;
    private final OrderRefundRepository orderRefundRepository;
    private final OrderRefundItemRepository orderRefundItemRepository;
    private final OrderChangeLogService orderChangeLogService;
    private final OrderPaymentRepository orderPaymentRepository;
    private final OrderItemGroupRepository orderItemGroupRepository;
    private final WarehouseRepository warehouseRepository;
    private final InventoryService inventoryService;
    private final PromotionRepository promotionRepository;
    private final DispatchRepairRepository dispatchRepairRepository;
    private final DispatchRepairService dispatchRepairService;

    @Override
    public OrderDetailDto createOrder(BaseOrderRequestDto orderRequestDto) {
        //log.debug("Create Order RequestDto: {}", orderRequestDto);

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Customer customer = resolveCustomer(orderRequestDto, currentUser);

        Order order = new Order();

        // ** START of new logic **
        // Handle Store or Distributor exclusivity
        if (orderRequestDto.getStoreId() != null && orderRequestDto.getDistributorId() != null) {
            throw new BadRequestException("一張訂單不能同時關聯門市和經銷商。");
        }

        if (orderRequestDto.getStoreId() != null) {
            StoreEntity store = storeRepository.findById(orderRequestDto.getStoreId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到門市，ID: " + orderRequestDto.getStoreId()));
            order.setStore(store);
        } else if (orderRequestDto.getDistributorId() != null) {
            Distributor distributor = distributorRepository.findById(orderRequestDto.getDistributorId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到經銷商，ID: " + orderRequestDto.getDistributorId()));
            order.setDistributor(distributor);
        } else {
            throw new BadRequestException("訂單必須關聯一個門市或經銷商。");
        }
        // ** END of new logic **

        BeanUtils.copyProperties(orderRequestDto, order, "orderId", "items", "paymentDetails", "orderTypeCode", "createBy", "updateBy", "createTime", "updateTime", "isDeleted", "memberId", "customerName", "customerPhone", "storeId", "distributorId", "promotionId");

        // Handle Promotion
        if (orderRequestDto.getPromotionId() != null) {
            Promotion promotion = promotionRepository.findById(orderRequestDto.getPromotionId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到指定的優惠活動，ID: " + orderRequestDto.getPromotionId()));
            order.setPromotion(promotion);
        } else {
            order.setPromotion(null);
        }

        List<OrderItem> allOrderItems = new ArrayList<>();

        if (orderRequestDto instanceof DispatchProductOrderRequestDto dispatchDto) {
            //派工商品訂單邏輯
            //log.debug("Create Order dispatchDto: {}", dispatchDto);
            order.setOrderTypeCode(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode());
            BeanUtils.copyProperties(dispatchDto, order, "items", "paymentDetails");

            if (dispatchDto.getTechnicianId() != null) {
                UserAccount technician = userAccountRepository.findById(dispatchDto.getTechnicianId())
                        .orElseThrow(() -> new ResourceNotFoundException("找不到技師，ID: " + dispatchDto.getTechnicianId()));
                order.setTechnician(technician);
            }

            if (CollectionUtils.isEmpty(dispatchDto.getItems())) {
                throw new BadRequestException("派工商品訂單必須至少包含一個品項。");
            }

            for (OrderItemRequestDto itemDto : dispatchDto.getItems()) {
                OrderItem mainItem = createOrderItemFromDto(itemDto, order);

                if (!CollectionUtils.isEmpty(itemDto.getItemGroups())) {
                    List<OrderItemGroup> itemGroups = new ArrayList<>();
                    for (OrderItemGroupRequestDto groupDto : itemDto.getItemGroups()) {
                        OrderItemGroup group = new OrderItemGroup();
                        group.setOrderItem(mainItem);
                        group.setProductBarcode(groupDto.getProductBarcode());
                        group.setProductName(groupDto.getProductName());
                        group.setWarehouseId(groupDto.getWarehouseId());
                        group.setRequiresDispatch(groupDto.getRequiresDispatch());
                        group.setAwaitingMaterials(groupDto.getAwaitingMaterials());
                        group.setItemTypeCode(groupDto.getItemTypeCode());
                        group.setQuantity(groupDto.getQuantity());
                        group.setUnitPrice(groupDto.getUnitPrice());
                        group.setListPrice(groupDto.getListPrice());
                        group.setSubtotalAmount(groupDto.getUnitPrice().multiply(BigDecimal.valueOf(groupDto.getQuantity())));
                        group.setIsAwait(groupDto.getIsAwait() != null ? groupDto.getIsAwait() : 0);
                        itemGroups.add(group);
                    }
                    mainItem.setItemGroups(itemGroups);
                }
                allOrderItems.add(mainItem);
            }

        } else {
            //門市商品訂單邏輯
            // Logic for StoreProductOrder and other simple order types
            if (orderRequestDto instanceof StoreProductOrderRequestDto) {
                order.setOrderTypeCode(OrderTypeEnum.STORE_PRODUCT_ORDER.getCode());
            } else if (orderRequestDto.getOrderTypeCode() == null) {
                throw new BadRequestException("非派工訂單必須明確指定訂單類型代碼。");
            } else {
                order.setOrderTypeCode(orderRequestDto.getOrderTypeCode());
            }

            if (CollectionUtils.isEmpty(orderRequestDto.getItems())) {
                throw new BadRequestException("訂單品項不得為空");
            }
            for (OrderItemRequestDto itemDto : orderRequestDto.getItems()) {
                OrderItem item = createOrderItemFromDto(itemDto, order);
                item.setRequiresDispatch((short) 0);
                allOrderItems.add(item);
            }
        }

        order.setOrderId(UUID.randomUUID());
        order.setOrderNumber(generateOrderNumber(OrderTypeEnum.fromCode(order.getOrderTypeCode())));
        order.setUserAccount(currentUser);

        if (customer != null) {
            order.setMember(customer);
            order.setCustomerName(customer.getCustomerName());
            order.setCustomerPhone(customer.getPhoneNumber());
        } else if (StringUtils.hasText(orderRequestDto.getCustomerName()) && StringUtils.hasText(orderRequestDto.getCustomerPhone())){
            order.setCustomerName(orderRequestDto.getCustomerName());
            order.setCustomerPhone(orderRequestDto.getCustomerPhone());
            order.setMember(null); // Explicitly no member if only name/phone provided and not resolved to existing
        } else if (orderRequestDto.getMemberId() != null) {
             throw new ResourceNotFoundException("提供了會員ID但找不到對應會員: " + orderRequestDto.getMemberId());
        } // Else, it's an order without specific customer/member, fields remain null


        order.setOrderDate(orderRequestDto.getOrderDate() != null ? orderRequestDto.getOrderDate() : OffsetDateTime.now());
        order.setOrderStatusCode(Optional.ofNullable(orderRequestDto.getOrderStatusCode()).orElse(OrderStatusEnum.DRAFT.getCode()));
        order.setPaymentStatusCode(PaymentStatusEnum.UNPAID.getCode());

        // Auditing fields will be set by BaseEntity listener

        order.setItems(allOrderItems);

        // Final status determination
        if (orderRequestDto instanceof DispatchProductOrderRequestDto) {
            if (orderRequestDto.getOrderStatusCode() != null && orderRequestDto.getOrderStatusCode().equals(OrderStatusEnum.DRAFT.getCode())) {
                order.setOrderStatusCode(OrderStatusEnum.DRAFT.getCode());
            } else {
                // This is a "Submit" action for a new dispatch order
                // TODO: Implement actual price authority check logic here
                boolean priceExceedsAuthority = false; 
                if (priceExceedsAuthority) {
                    order.setOrderStatusCode(OrderStatusEnum.PENDING_STORE_APPROVAL.getCode()); // 20
                } else {
                    order.setOrderStatusCode(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode()); // 30
                }
            }
        } else {
            // For other order types, maintain the original logic
            order.setOrderStatusCode(Optional.ofNullable(orderRequestDto.getOrderStatusCode()).orElse(OrderStatusEnum.DRAFT.getCode()));
        }

        recalculateOrderTotals(order, orderRequestDto.getTaxType());

        // First, save the order to get a persistent entity
        Order savedOrder = orderRepository.save(order);

        // Now, process payments and create journal entries with the persistent order
        storeDailyJournalRepository.deleteByRelatedOrder_OrderId(order.getOrderId());
        processAndSavePayments(savedOrder, orderRequestDto.getPaymentDetails(), currentUser);

        // Save again to persist any changes made in processAndSavePayments (like paymentStatusCode)
        Order finalOrder = orderRepository.save(savedOrder);

        orderChangeLogService.logOrderChange(finalOrder, null, finalOrder.getOrderStatusCode(), currentUser, "訂單建立", orderRequestDto);

        return convertToOrderDetailDto(finalOrder);
    }

    private void processAndSavePayments(Order order, List<PaymentDetailDto> paymentDtos, UserAccount currentUser) {
        // Ensure the payments collection is not null
        if (order.getPayments() == null) {
            order.setPayments(new ArrayList<>());
        }

        // Clear existing payments for this specific order to avoid duplicates
        order.getPayments().clear();

        if (CollectionUtils.isEmpty(paymentDtos)) {
            order.setPaidAmount(BigDecimal.ZERO);
            order.setPaymentStatusCode(PaymentStatusEnum.UNPAID.getCode());
            return;
        }

        BigDecimal totalPaid = BigDecimal.ZERO;
        List<OrderPayment> newPayments = new ArrayList<>();
        for (PaymentDetailDto dto : paymentDtos) {
            if (dto.getAmount() != null && dto.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                OrderPayment payment = new OrderPayment();
                payment.setOrder(order);
                payment.setPaymentMethodCode(dto.getPaymentMethodCode().name());
                payment.setAmount(dto.getAmount());
                payment.setPaymentTime(OffsetDateTime.now());

                payment.setCardNumber(dto.getCardNumber());
                payment.setCardBrand(dto.getCardBrand());
                payment.setCardIssuer(dto.getCardIssuer());
                payment.setCardInstallments(dto.getCardInstallments());
                payment.setBankName(dto.getBankName());
                payment.setBankAccountLastFive(dto.getBankAccountLastFive());
                payment.setRemitterAccountLastFive(dto.getRemitterAccountLastFive());
                payment.setCollectorName(dto.getCollectorName());
                payment.setRemarks(dto.getRemarks());

                newPayments.add(payment);
                totalPaid = totalPaid.add(dto.getAmount());
                //createJournalEntryForPayment(order, payment, currentUser);
            }
        }

        // Add all new payments to the (now cleared) original collection
        order.getPayments().addAll(newPayments);
        order.setPaidAmount(totalPaid);

        // Update payment status based on the new totalPaid
        if (totalPaid.compareTo(order.getGrandTotalAmount()) >= 0 && order.getGrandTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            order.setPaymentStatusCode(PaymentStatusEnum.FULLY_PAID.getCode());
        } else if (totalPaid.compareTo(BigDecimal.ZERO) > 0) {
            order.setPaymentStatusCode(PaymentStatusEnum.PARTIALLY_PAID.getCode());
        } else {
            order.setPaymentStatusCode(PaymentStatusEnum.UNPAID.getCode());
        }
    }

    private void createJournalEntryForPayment(Order order, OrderPayment payment, UserAccount currentUser) {
        StoreDailyJournal journalEntry = StoreDailyJournal.builder()
            .store(order.getStore())
            .entryDatetime(payment.getPaymentTime())
            .transactionTypeCode(StoreJournalTransactionTypeEnum.SALE_RECEIPT.getCode())
            .relatedOrder(order)
            .relatedDocumentNumber(order.getOrderNumber())
            .paymentMethodCode(PaymentMethodEnum.valueOf(payment.getPaymentMethodCode()).getCode())
            .amountIn(payment.getAmount())
            .amountOut(BigDecimal.ZERO)
            .description("訂單付款: " + payment.getPaymentMethodCode())
            .userAccount(currentUser) 
            .build();
        storeDailyJournalRepository.save(journalEntry);
    }

    private Customer resolveCustomer(BaseOrderRequestDto orderRequestDto, UserAccount currentUser) {
        if (orderRequestDto.getMemberId() != null) {
            return customerRepository.findById(orderRequestDto.getMemberId())
                .filter(c -> c.getIsDeleted() == 0)
                .orElseThrow(() -> new ResourceNotFoundException("找不到指定的會員ID: " + orderRequestDto.getMemberId()));
        }
        if (StringUtils.hasText(orderRequestDto.getCustomerPhone())) {
            Optional<Customer> existingCustomerOpt = customerRepository.findByPhoneNumberAndIsDeleted(orderRequestDto.getCustomerPhone(), (short)0);
            if (existingCustomerOpt.isPresent()) {
                return existingCustomerOpt.get();
            }
            if (StringUtils.hasText(orderRequestDto.getCustomerName())) {
                Customer newCustomer = new Customer();
                newCustomer.setCustomerName(orderRequestDto.getCustomerName());
                newCustomer.setPhoneNumber(orderRequestDto.getCustomerPhone());
                newCustomer.setCompanyDivisionCode(orderRequestDto.getCompanyDivisionCode()); 
                if (orderRequestDto instanceof DispatchProductOrderRequestDto) {
                    newCustomer.setFullAddress(((DispatchProductOrderRequestDto) orderRequestDto).getInstallationAddress());
                } else {
                     newCustomer.setFullAddress(orderRequestDto.getInvoiceAddress());
                }
                return customerRepository.save(newCustomer);
            }
        }
        if ( (orderRequestDto.getMemberId() == null) && 
             (!StringUtils.hasText(orderRequestDto.getCustomerName()) || !StringUtils.hasText(orderRequestDto.getCustomerPhone())) ){
            // Allow orders without specific customer if that's a business requirement for some order types.
            // For now, if not providing memberId, name AND phone are required to create/find a customer.
            // Otherwise, it's an anonymous order, and customerName/customerPhone on the Order entity itself will be used if provided directly in DTO.
        }
        return null; 
    }

    private String generateOrderNumber(OrderTypeEnum orderType) {
        String prefix = "ORD"; 
        if (orderType != null) {
            switch (orderType) {
                case STORE_PRODUCT_ORDER: prefix = "M"; break;
                case DISPATCH_PRODUCT_ORDER: prefix = "D"; break;
                case WHOLESALE_ORDER: prefix = "W"; break;
            }
        }
        String datePart = OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // Generate a 6-digit random number to reduce collision probability significantly compared to 3-digit
        String randomPart = String.format("%06d", new Random().nextInt(1000000)); 
        // Consider adding a millisecond part or a sequence for higher collision resistance if needed
        return prefix + datePart + randomPart;
    }

    @Override
    @Transactional(readOnly = true)
    public OrderDetailDto getOrderById(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        // 獲取最新的退貨單
        OrderRefund latestRefund = orderRefundRepository.findLatestRefundsForOrders(Collections.singletonList(orderId))
            .stream().findFirst().orElse(null);
        //log.debug("getOrderById orderId/latestRefund: {}/{}", order.getOrderId(), latestRefund != null ? latestRefund.getOrderRefundId() : "null");

        // Re-use the warehouse name fetching logic here as well
        Map<UUID, String> warehouseNameMap = new HashMap<>();
        if (order.getItems() != null) {
            Set<UUID> warehouseIds = new HashSet<>();
            order.getItems().forEach(item -> {
                if (item.getWarehouseCode() != null) {
                    try {
                        warehouseIds.add(UUID.fromString(item.getWarehouseCode()));
                    } catch (IllegalArgumentException e) {
                        // Ignore if warehouseCode is not a valid UUID
                    }
                }
                if (item.getItemGroups() != null) {
                    item.getItemGroups().forEach(group -> {
                        if (group.getWarehouseId() != null) {
                            warehouseIds.add(group.getWarehouseId());
                        }
                    });
                }
            });

            if (!warehouseIds.isEmpty()) {
                List<Warehouse> warehouses = warehouseRepository.findAllById(warehouseIds);
                warehouseNameMap.putAll(warehouses.stream()
                    .collect(Collectors.toMap(Warehouse::getWarehouseId, Warehouse::getWarehouseName)));
            }
        }

        return convertToOrderDetailDto(order, latestRefund, warehouseNameMap);
    }

    @Override
    @Transactional(readOnly = true)
    public OrderSummaryDto getOrderSummaryById(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));
        return convertToOrderSummaryDto(order);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrderSummaryDto> searchOrders(
            Short companyDivisionCode, Short orderTypeCode, String orderNumber, 
            String customerKeyword, OffsetDateTime orderDateFrom, OffsetDateTime orderDateTo, 
            List<Short> orderStatusCodes, UUID storeId, Pageable pageable) {

        Specification<Order> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));

            if (companyDivisionCode != null) {
                predicates.add(cb.equal(root.get("companyDivisionCode"), companyDivisionCode));
            }
            if (orderTypeCode != null) {
                predicates.add(cb.equal(root.get("orderTypeCode"), orderTypeCode));
            }
            if (StringUtils.hasText(orderNumber)) {
                predicates.add(cb.like(cb.lower(root.get("orderNumber")), "%" + orderNumber.toLowerCase() + "%"));
            }
            if (StringUtils.hasText(customerKeyword)) {
                Predicate namePredicate = cb.like(cb.lower(root.get("customerName")), "%" + customerKeyword.toLowerCase() + "%");
                Predicate phonePredicate = cb.like(cb.lower(root.get("customerPhone")), "%" + customerKeyword.toLowerCase() + "%");
                predicates.add(cb.or(namePredicate, phonePredicate));
            }
            if (orderDateFrom != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("orderDate"), orderDateFrom));
            }
            if (orderDateTo != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("orderDate"), orderDateTo));
            }
            if (!CollectionUtils.isEmpty(orderStatusCodes)) {
                predicates.add(root.get("orderStatusCode").in(orderStatusCodes));
            }
            if (storeId != null) {
                predicates.add(cb.equal(root.get("store").get("storeId"), storeId));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 使用自定義查詢方法，當有 updateTime 排序時會考慮退貨單的 updateTime
        Page<Order> orderPage = orderRepository.findAllWithRefundConsideration(spec, pageable);

        // 解決 N+1 問題: 一次性獲取所有相關的退貨單
        List<UUID> orderIds = orderPage.getContent().stream().map(Order::getOrderId).collect(Collectors.toList());
        Map<UUID, OrderRefund> latestRefundsMap = orderRefundRepository.findLatestRefundsForOrders(orderIds)
                .stream()
                .collect(Collectors.toMap(refund -> refund.getOriginalOrder().getOrderId(), refund -> refund, (r1, r2) -> r1)); // 如果有多筆，取第一個

        return orderPage.map(order -> convertToOrderSummaryDto(order, latestRefundsMap.get(order.getOrderId())));
    }

    // --- Status Transition Methods (Stubs - to be implemented based on ********訂單流程v2.txt) ---

    @Override
    public OrderDetailDto processCheckout(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();

        if (!Objects.equals(originalStatus, OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode())) { 
            throw new BadRequestException("只有狀態為 '門市結帳' 的訂單才能執行結帳送出。");
        }

        Short newStatus;

        if (Objects.equals(order.getOrderTypeCode(), OrderTypeEnum.STORE_PRODUCT_ORDER.getCode())){
            // Deduct stock for store products
            for (OrderItem item : order.getItems()) {
                warehouseStoreInventoryService.updateInventory(
                    order.getStore().getStoreId(),
                    item.getProductBarcode(),
                    item.getProductName(),
                    -item.getQuantity(),
                    InventoryTransactionTypeEnum.SALE.name(),
                    order.getOrderId().toString()
                );
            }
            newStatus = OrderStatusEnum.SHIPPED_CLOSED.getCode();
        } else { 
            // Logic for Dispatch and Wholesale orders
            boolean stockIsSufficient = checkHqStock(order.getItems());
            if (stockIsSufficient) {
                // ... (pre-deduct stock logic)
                newStatus = OrderStatusEnum.PENDING_HQ_APPROVAL.getCode();
            } else {
                newStatus = OrderStatusEnum.HQ_STOCK_INSUFFICIENT.getCode();
            }
        }

        order.setOrderStatusCode(newStatus);
        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "結帳送出", null);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public OrderDetailDto confirmHqApproval(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();

        if (!Objects.equals(originalStatus, OrderStatusEnum.PENDING_HQ_APPROVAL.getCode())){
            throw new BadRequestException("訂單狀態不正確，無法執行總公司核准。");
        }

        order.setOrderStatusCode(OrderStatusEnum.HQ_APPROVED.getCode()); 

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, updatedOrder.getOrderStatusCode(), currentUser, "總公司核准", null);
        return convertToOrderDetailDto(updatedOrder);
    }

    private boolean checkHqStock(List<OrderItem> items) {
        // Placeholder for real stock check logic against a central warehouse inventory
        return true; 
    }

    @Override
    public OrderDetailDto markOrderShippedOrDispatchCompleted(UUID orderId) {
         Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        boolean canComplete = false;
        Short originalStatus = order.getOrderStatusCode();
        String currentStatusDesc = Optional.ofNullable(OrderStatusEnum.fromCode(originalStatus)).map(OrderStatusEnum::getDescription).orElse("未知");

        if (Objects.equals(order.getOrderTypeCode(), OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode())) { 
            if (Objects.equals(originalStatus, OrderStatusEnum.DISPATCH_PENDING_PICKUP.getCode())) {
                canComplete = true;
                order.setOrderStatusCode(OrderStatusEnum.SHIPPED_CLOSED.getCode());
                order.setActualCompletionDate(OffsetDateTime.now());
            }
        }
        else if (Objects.equals(order.getOrderTypeCode(), OrderTypeEnum.WHOLESALE_ORDER.getCode())) { 
            if (Objects.equals(originalStatus, OrderStatusEnum.HQ_APPROVED.getCode())) { 
                canComplete = true;
                order.setOrderStatusCode(OrderStatusEnum.SHIPPED_CLOSED.getCode());
            }
        }

        if (!canComplete) {
            throw new BadRequestException("訂單類型 " + Optional.ofNullable(OrderTypeEnum.fromCode(order.getOrderTypeCode())).map(OrderTypeEnum::getDescription).orElse("未知") + " 的狀態 ("+ currentStatusDesc + ") 不正確，無法完成出貨/派工。");
        }

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, updatedOrder.getOrderStatusCode(), currentUser, "訂單完成出貨/派工", null);
        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public OrderDetailDto requestOrderCancellation(UUID orderId, String reason) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        String currentStatusDesc = Optional.ofNullable(OrderStatusEnum.fromCode(order.getOrderStatusCode())).map(OrderStatusEnum::getDescription).orElse("未知");
        String orderTypeDesc = Optional.ofNullable(OrderTypeEnum.fromCode(order.getOrderTypeCode())).map(OrderTypeEnum::getDescription).orElse("未知");

        if (Objects.equals(order.getOrderTypeCode(), OrderTypeEnum.STORE_PRODUCT_ORDER.getCode())) {
            if (Objects.equals(order.getOrderStatusCode(), OrderStatusEnum.DRAFT.getCode())) {
                order.setIsDeleted((short) 1); // Soft delete
                order.setOrderStatusCode(OrderStatusEnum.CANCEL_APPROVED_BY_HQ.getCode()); // Directly to 'approved cancellation'
                order.setRemarks((order.getRemarks() == null ? "" : order.getRemarks() + "\n") + "門市草稿訂單取消: " + reason);
                auditLogService.logAction(AuditActionTypeEnum.DELETE, AuditDataTypeEnum.ORDER, order.getOrderId().toString(), "門市草稿訂單取消: " + order.getOrderNumber(), reason);
            } else if (order.getOrderStatusCode() >= OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode() && order.getOrderStatusCode() < OrderStatusEnum.SHIPPED_CLOSED.getCode()){
                 // According to ********訂單流程v2.txt, store orders after checkout (30) but before closed (70) can request cancellation.
                 // This is specific for store orders if they haven't been fully closed out (e.g. pending stock return if items were picked).
                 // For simplicity now, we make it request HQ approval. More complex logic for stock return would be in approveOrderCancellation.
                 order.setOrderStatusCode(OrderStatusEnum.CANCEL_PENDING_HQ_APPROVAL.getCode());
                 order.setRemarks((order.getRemarks() == null ? "" : order.getRemarks() + "\n") + "門市訂單請求取消: " + reason);
                 auditLogService.logAction(AuditActionTypeEnum.UPDATE, AuditDataTypeEnum.ORDER, order.getOrderId().toString(), "門市訂單請求取消: " + order.getOrderNumber() + ", 原狀態: " + currentStatusDesc, reason);
            } else if (order.getOrderStatusCode() >= OrderStatusEnum.SHIPPED_CLOSED.getCode()) {
                 throw new BadRequestException("門市訂單 (" + order.getOrderNumber() + ") 已結案 (" + currentStatusDesc + ")，無法直接取消，請執行退貨流程。");
            } else {
                throw new BadRequestException("門市訂單 (" + order.getOrderNumber() + ") 狀態 ("+currentStatusDesc+") 不允許直接取消，請確認流程或執行退貨。");
            }
        } else if (Objects.equals(order.getOrderTypeCode(), OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode()) || 
                   Objects.equals(order.getOrderTypeCode(), OrderTypeEnum.WHOLESALE_ORDER.getCode())) {

            if (order.getOrderStatusCode() >= OrderStatusEnum.SHIPPED_CLOSED.getCode()) { 
                 throw new BadRequestException(orderTypeDesc + " (" + order.getOrderNumber() + ") 已完成或關閉 (" + currentStatusDesc + ")，無法請求取消，請確認是否需要執行退貨流程。");
            }
            // Allow cancellation request for most other preceding states
            order.setOrderStatusCode(OrderStatusEnum.CANCEL_PENDING_HQ_APPROVAL.getCode()); 
            order.setRemarks((order.getRemarks() == null ? "" : order.getRemarks() + "\n") + orderTypeDesc + "請求取消: " + reason);
            auditLogService.logAction(AuditActionTypeEnum.UPDATE, AuditDataTypeEnum.ORDER, order.getOrderId().toString(), orderTypeDesc + "請求取消訂單: " + order.getOrderNumber() + ", 原狀態: " + currentStatusDesc, reason);
        } else {
            throw new BadRequestException("未知的訂單類型 ("+orderTypeDesc+")，無法處理取消請求。");
        }

        Order updatedOrder = orderRepository.save(order);
        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public OrderDetailDto approveOrderCancellation(UUID orderId, UUID approverId) { 
        OrderRefund refund = orderRefundRepository.findByOriginalOrder_OrderIdAndIsDeleted(orderId, (short)0).stream().findFirst()
             .orElseThrow(() -> new ResourceNotFoundException("找不到對應的取消單，訂單ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取審核員信息"));

        Short originalStatus = refund.getRefundStatusCode();
        // ... (status change logic)

        refund.setRefundStatusCode(OrderStatusEnum.CANCEL_APPROVED_BY_HQ.getCode());
        OrderRefund updatedRefund = orderRefundRepository.save(refund);
        orderChangeLogService.logRefundChange(updatedRefund, originalStatus, updatedRefund.getRefundStatusCode(), currentUser, "訂單取消已核准", null);

        return convertToOrderDetailDto(updatedRefund.getOriginalOrder());
    }

    @Override
    public OrderDetailDto rejectOrderCancellation(UUID orderId, UUID approverId, String rejectionReason) {
        OrderRefund refund = orderRefundRepository.findByOriginalOrder_OrderIdAndIsDeleted(orderId, (short)0).stream().findFirst()
             .orElseThrow(() -> new ResourceNotFoundException("找不到對應的取消單，訂單ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取審核員信息"));
        Short originalStatus = refund.getRefundStatusCode();
        //... (status change logic)

        refund.setRefundStatusCode(OrderStatusEnum.CANCEL_REJECTED_BY_HQ.getCode());
        OrderRefund updatedRefund = orderRefundRepository.save(refund);
        orderChangeLogService.logRefundChange(updatedRefund, originalStatus, updatedRefund.getRefundStatusCode(), currentUser, "訂單取消被駁回", rejectionReason);

        return convertToOrderDetailDto(updatedRefund.getOriginalOrder());
    }

    @Override
    public OrderDetailDto updateOrderForCorrection(UUID orderId, BaseOrderRequestDto orderRequestDto) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到待修正的訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatusCode = order.getOrderStatusCode();
        String originalStatusDesc = Optional.ofNullable(OrderStatusEnum.fromCode(originalStatusCode)).map(OrderStatusEnum::getDescription).orElse("未知");

        boolean isEditable = (originalStatusCode < 40 && originalStatusCode != 30 && originalStatusCode != 33) || 
                             originalStatusCode.equals(OrderStatusEnum.AWAITING_CORRECTION_HQ.getCode());

        if (!isEditable) {
            throw new BadRequestException("訂單狀態 (" + originalStatusDesc + ") 不允許修正。");
        }

        // --- Start of Refactored Update Logic ---

        // Update non-collection fields first
        BeanUtils.copyProperties(orderRequestDto, order, "items", "paymentDetails", "orderId", "orderNumber", "orderDate", "createTime", "updateTime", "createBy", "updateBy", "orderStatusCode");

        // Update customer details
        Customer customer = resolveCustomer(orderRequestDto, currentUser);
        order.setMember(customer); // This will handle both existing and new customers
        if (customer != null) {
            order.setCustomerName(customer.getCustomerName());
            order.setCustomerPhone(customer.getPhoneNumber());
        }

        // Handle Promotion update
        if (orderRequestDto.getPromotionId() != null) {
            Promotion promotion = promotionRepository.findById(orderRequestDto.getPromotionId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到指定的優惠活動，ID: " + orderRequestDto.getPromotionId()));
            order.setPromotion(promotion);
        } else {
            order.setPromotion(null);
        }

        // Clear existing items to trigger orphanRemoval and re-create from DTO
        order.getItems().clear(); 

        List<OrderItem> allOrderItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderRequestDto.getItems())) {
            if (orderRequestDto instanceof DispatchProductOrderRequestDto dispatchDto) {
                //派工商品訂單邏輯
                // Dispatch Order specific logic
                for (OrderItemRequestDto itemDto : dispatchDto.getItems()) {
                    OrderItem mainItem = createOrderItemFromDto(itemDto, order);
                    if (!CollectionUtils.isEmpty(itemDto.getItemGroups())) {
                        List<OrderItemGroup> itemGroups = new ArrayList<>();
                        for (OrderItemGroupRequestDto groupDto : itemDto.getItemGroups()) {
                            OrderItemGroup group = new OrderItemGroup();
                            group.setOrderItem(mainItem);
                            group.setProductBarcode(groupDto.getProductBarcode());
                            group.setProductName(groupDto.getProductName());
                            group.setWarehouseId(groupDto.getWarehouseId());
                            group.setRequiresDispatch(groupDto.getRequiresDispatch());
                            group.setAwaitingMaterials(groupDto.getAwaitingMaterials());
                            group.setItemTypeCode(groupDto.getItemTypeCode());
                            group.setQuantity(groupDto.getQuantity());
                            group.setUnitPrice(groupDto.getUnitPrice());
                            group.setListPrice(groupDto.getListPrice());
                            group.setSubtotalAmount(groupDto.getUnitPrice().multiply(BigDecimal.valueOf(groupDto.getQuantity())));
                            group.setIsAwait(groupDto.getIsAwait() != null ? groupDto.getIsAwait() : 0);
                            itemGroups.add(group);
                        }
                        mainItem.setItemGroups(itemGroups);
                    }
                    allOrderItems.add(mainItem);
                }
            } else {
                //門市商品訂單邏輯
                // Generic/Store Order logic
                for (OrderItemRequestDto itemDto : orderRequestDto.getItems()) {
                    itemDto.setRequiresDispatch((short) 0);
                    allOrderItems.add(createOrderItemFromDto(itemDto, order));
                }
            }
        }
        order.getItems().addAll(allOrderItems);

        recalculateOrderTotals(order, orderRequestDto.getTaxType());
        processAndSavePayments(order, orderRequestDto.getPaymentDetails(), currentUser);

        // Status transition logic remains the same
        // ... (existing status transition logic)

        Order updatedOrder = orderRepository.save(order);

        orderChangeLogService.logOrderChange(updatedOrder, originalStatusCode, updatedOrder.getOrderStatusCode(), currentUser, "訂單修正", orderRequestDto);

        return convertToOrderDetailDto(updatedOrder);
    }

    private void recalculateOrderTotals(Order order, Short taxType) {
        BigDecimal itemsSubtotalNet = BigDecimal.ZERO;
        BigDecimal itemsSubtotalGross = BigDecimal.ZERO;

        if (order.getItems() != null) {
            for (OrderItem item : order.getItems()) {
                itemsSubtotalNet = itemsSubtotalNet.add(item.getSubtotalAmount());
                itemsSubtotalGross = itemsSubtotalGross.add(item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())));
            }
        }

        order.setProductsTotalAmount(itemsSubtotalGross);
        order.setNetAmount(itemsSubtotalNet);
        order.setDiscountAmount(itemsSubtotalGross.subtract(itemsSubtotalNet));

        // 根據發票類型決定如何計算稅金
        BigDecimal taxAmount = BigDecimal.ZERO;
        BigDecimal grandTotal = itemsSubtotalNet;

        if (order.getInvoiceTypeCode() != null && 
            (order.getInvoiceTypeCode().equals(InvoiceTypeEnum.TWO_PART.getCode()) || 
             order.getInvoiceTypeCode().equals(InvoiceTypeEnum.THREE_PART.getCode()))) {

            BigDecimal taxRate = new BigDecimal("0.05"); // 5% 稅率

            if (taxType != null && TaxTypeEnum.INCLUSIVE.getCode()==(short)taxType) {
                // 稅內：總額保持不變，反推淨額和稅額
                grandTotal = itemsSubtotalNet; // 總額不變
                BigDecimal netAmountFromTotal = grandTotal.divide(new BigDecimal("1.05"), 0, RoundingMode.HALF_UP);
                taxAmount = grandTotal.subtract(netAmountFromTotal);
                order.setNetAmount(netAmountFromTotal);
            } else { // 稅外 (exclusive) 或 null (預設稅外)
                // 稅外：淨額不變，計算稅額和總額
                taxAmount = itemsSubtotalNet.multiply(taxRate).setScale(0, RoundingMode.HALF_UP);
                grandTotal = itemsSubtotalNet.add(taxAmount);
            }
        }

        order.setTaxAmount(taxAmount);
        order.setGrandTotalAmount(grandTotal);
    }

    // 為了向後相容，保留原方法，但內部調用新方法
    private void recalculateOrderTotals(Order order) {
        recalculateOrderTotals(order, (short)1); // 預設為稅外 (1)
    }

    @Override
    public OrderDetailDto requestOrderReturn(OrderRefundRequestDto refundRequest) {
        Order order = orderRepository.findById(refundRequest.getOrderId())
                .filter(o -> o.getIsDeleted() == 0)
                .orElseThrow(() -> new ResourceNotFoundException("找不到要退貨的訂單，ID: " + refundRequest.getOrderId()));

        // --- Business Logic Validation ---
        // 1. Check if order status is eligible for return
        if (order.getOrderStatusCode() < OrderStatusEnum.SHIPPED_CLOSED.getCode()) {
            throw new BadRequestException("訂單尚未結案，無法申請退貨。請先取消訂單。");
        }
        if (order.getOrderStatusCode().equals(OrderStatusEnum.RETURN_PENDING_HQ_APPROVAL.getCode())) {
            throw new BadRequestException("訂單已在退貨審核中，請勿重複申請。");
        }

        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setOrderRefundId(UUID.randomUUID());
        orderRefund.setOriginalOrder(order);
        orderRefund.setReason(refundRequest.getReason());
        orderRefund.setRefundStatusCode(OrderStatusEnum.RETURN_PENDING_HQ_APPROVAL.getCode());
        orderRefund.setRequestDate(OffsetDateTime.now());

        List<OrderRefundItem> refundItems = new ArrayList<>();
        BigDecimal totalRefundAmount = BigDecimal.ZERO;

        for (OrderRefundItemRequestDto itemDto : refundRequest.getItemsToReturn()) {
            OrderItem originalItem = order.getItems().stream()
                    .filter(i -> i.getOrderItemId().equals(itemDto.getOrderItemId()))
                    .findFirst()
                    .orElseThrow(() -> new ResourceNotFoundException("退貨品項不在原訂單中，品項ID: " + itemDto.getOrderItemId()));

            // Check if quantity to return is valid
            int alreadyReturnedQty = orderRefundRepository.findAllByOriginalOrder_OrderId(order.getOrderId()).stream()
                .flatMap(refund -> refund.getItems().stream())
                .filter(refundItem -> refundItem.getOriginalOrderItem().getOrderItemId().equals(originalItem.getOrderItemId()))
                .mapToInt(OrderRefundItem::getQuantity)
                .sum();

            if (itemDto.getQuantity() > (originalItem.getQuantity() - alreadyReturnedQty)) {
                throw new BadRequestException("退貨數量 " + itemDto.getQuantity() + " 超過可退貨數量 " + (originalItem.getQuantity() - alreadyReturnedQty));
            }

            OrderRefundItem refundItem = new OrderRefundItem();
            refundItem.setOrderRefundItemId(UUID.randomUUID());
            refundItem.setOrderRefund(orderRefund);
            refundItem.setOriginalOrderItem(originalItem);
            refundItem.setQuantity(itemDto.getQuantity());

            // Calculate refund amount for this item based on its final price in original order
            BigDecimal refundItemAmount = originalItem.getFinalPricePerItem().multiply(new BigDecimal(itemDto.getQuantity()));
            refundItem.setSubtotalRefundAmount(refundItemAmount);
            totalRefundAmount = totalRefundAmount.add(refundItemAmount);

            refundItems.add(refundItem);
        }

        orderRefund.setItems(refundItems);
        orderRefund.setExpectedRefundAmount(totalRefundAmount);

        // Save the refund request
        orderRefundRepository.save(orderRefund);

        // Update original order status
        order.setOrderStatusCode(OrderStatusEnum.RETURN_PENDING_HQ_APPROVAL.getCode());
        orderRepository.save(order);

        auditLogService.logAction(
            AuditActionTypeEnum.CREATE,
            AuditDataTypeEnum.ORDER_REFUND,
            orderRefund.getOrderRefundId().toString(),
            "發起訂單退貨申請，原訂單: " + order.getOrderNumber(),
            refundRequest.getReason()
        );

        return convertToOrderDetailDto(order);
    }

    @Override
    @Transactional
    public OrderDetailDto checkoutStoreOrder(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();

        // 僅允許從特定狀態進行結帳
        if (!originalStatus.equals(OrderStatusEnum.DRAFT.getCode()) && !originalStatus.equals(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode())) {
            throw new BadRequestException("訂單狀態不正確，無法執行結帳送出。當前狀態: " + OrderStatusEnum.fromCode(originalStatus).getDescription());
        }

        // 檢查庫存並扣除
        for (OrderItem item : order.getItems()) {
            try {
                warehouseStoreInventoryService.updateInventory(
                    order.getStore().getStoreId(),
                    item.getProductBarcode(),
                    item.getProductName(),
                    -item.getQuantity(),
                    InventoryTransactionTypeEnum.SALE.name(),
                    order.getOrderId().toString()
                );
            } catch (Exception e) {
                // 如果庫存不足或其他錯誤，直接向上拋出異常
                throw new BadRequestException("庫存操作失敗: " + e.getMessage());
            }
        }

        // 記錄日記帳
        for (OrderPayment payment : order.getPayments()) {
             createJournalEntryForPayment(order, payment, currentUser);
        }

        // 更新訂單狀態
        Short newStatus = OrderStatusEnum.SHIPPED_CLOSED.getCode();
        order.setOrderStatusCode(newStatus);

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "結帳送出並完成", null);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public OrderDetailDto storeApprove(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();
        if (!originalStatus.equals(OrderStatusEnum.PENDING_STORE_APPROVAL.getCode())) {
            throw new BadRequestException("訂單狀態不正確，無法執行店經理核准。");
        }

        // For Store Product Orders, directly go to STORE_CHECKOUT_COMPLETE status per business rule
        Short newStatus;
        if (order.getOrderTypeCode().equals(OrderTypeEnum.STORE_PRODUCT_ORDER.getCode())) {
            // 對於門市訂單，"送出"僅意味著保存草稿，狀態不變。
            // 真正的狀態變更將由詳情頁的 "結帳送出" 按鈕觸發，呼叫 checkoutStoreOrder
            newStatus = OrderStatusEnum.DRAFT.getCode();
        } else {
             // 對於派工和批發訂單，檢查價格權限
            boolean priceExceedsAuthority = false;
            if (priceExceedsAuthority) {
                newStatus = OrderStatusEnum.PENDING_STORE_APPROVAL.getCode(); // 狀態 20: 待店經理審核
            } else {
                // 原本是 STORE_CHECKOUT_COMPLETE(30)，但根據新流程，這裡應該是總公司審核
                // 為了與流程圖v2對齊，這裡先暫定為 PENDING_HQ_APPROVAL(40)
                 newStatus = OrderStatusEnum.PENDING_HQ_APPROVAL.getCode(); // 狀態 40: 待總公司審核
            }
        }

        order.setOrderStatusCode(newStatus);
        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "店經理核准", null);
        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public OrderDetailDto storeReturnForCorrection(UUID orderId, String reason) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();
        if (!originalStatus.equals(OrderStatusEnum.PENDING_STORE_APPROVAL.getCode())) {
            throw new BadRequestException("訂單狀態不正確，無法退回補正。");
        }

        Short newStatus = OrderStatusEnum.AWAITING_CORRECTION_STORE.getCode();
        order.setOrderStatusCode(newStatus);

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "店經理退回補正", reason);
        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public OrderDetailDto submitHqFromStockFull(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();
        if (!originalStatus.equals(OrderStatusEnum.HQ_STOCK_FULL.getCode())) {
            throw new BadRequestException("只有在庫存充足（已有庫存）狀態下才能送出審核。");
        }

        Short newStatus = OrderStatusEnum.PENDING_HQ_APPROVAL.getCode();
        order.setOrderStatusCode(newStatus);

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "已有庫存，送至總公司審核", null);
        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    @Transactional
    public OrderDetailDto hqReturnForCorrection(UUID orderId, String reason) {
        // Similar logic, changing status from PENDING_HQ_APPROVAL (40) to AWAITING_CORRECTION_HQ (43)
        return updateOrderStatus(orderId, OrderStatusEnum.PENDING_HQ_APPROVAL, OrderStatusEnum.AWAITING_CORRECTION_HQ, "總公司退回補正", reason);
    }

    @Override
    public OrderDetailDto hqReject(UUID orderId, String reason) {
        // Similar logic, changing status from PENDING_HQ_APPROVAL (40) to HQ_REJECTED (46)
        return updateOrderStatus(orderId, OrderStatusEnum.PENDING_HQ_APPROVAL, OrderStatusEnum.HQ_REJECTED, "總公司駁回訂單", reason);
    }

    @Override
    public OrderDetailDto hqApprove(UUID orderId) {
        return updateOrderStatus(orderId, OrderStatusEnum.PENDING_HQ_APPROVAL, OrderStatusEnum.HQ_APPROVED, "總公司審核通過", null);
    }

    @Override
    public OrderDetailDto submitForHqReview(UUID orderId) {
        return updateOrderStatus(orderId, OrderStatusEnum.AWAITING_CORRECTION_HQ, OrderStatusEnum.PENDING_HQ_APPROVAL, "訂單補正後重新送審", null);
    }

    @Override
    public OrderDetailDto approveOrderReturn(UUID refundId) {
        OrderRefund refund = orderRefundRepository.findById(refundId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到退貨單，ID: " + refundId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = refund.getRefundStatusCode();

        if (!originalStatus.equals(OrderStatusEnum.RETURN_PENDING_HQ_APPROVAL.getCode())) {
            throw new BadRequestException("退貨單狀態不正確，無法核准。");
        }

        // Stock Reversal Logic
        for (OrderRefundItem item : refund.getItems()) {
            try {
                // Assuming restock to the original store if warehouse not specified
                UUID restockLocationId = StringUtils.hasText(item.getRestockWarehouseCode()) ? UUID.fromString(item.getRestockWarehouseCode()) : refund.getOriginalOrder().getStore().getStoreId();
                warehouseStoreInventoryService.updateInventory(restockLocationId, item.getProductBarcode(), item.getProductName(), item.getQuantity(), InventoryTransactionTypeEnum.RETURN_RESTOCK.name(), refund.getOrderRefundId().toString());
            } catch (Exception e) {
                // Log stock reversal failure but continue the process
                orderChangeLogService.logRefundChange(refund, null, null, currentUser, "退貨核准-庫存歸還失敗", e.getMessage());
            }
        }

        boolean isPartialReturn = checkIfPartialReturn(refund);
        Short newStatus = isPartialReturn ? 
            OrderStatusEnum.RETURN_PART_APPROVED_PENDING_PICKUP.getCode() : 
            OrderStatusEnum.RETURN_APPROVED_PENDING_PICKUP.getCode();

        if (isPartialReturn) {
            // Create a new order with the remaining items
            createNewOrderFromPartialReturn(refund.getOriginalOrder(), refund, currentUser);
        }

        refund.setRefundStatusCode(newStatus);
        OrderRefund updatedRefund = orderRefundRepository.save(refund);
        orderChangeLogService.logRefundChange(updatedRefund, originalStatus, newStatus, currentUser, "總公司核准退貨", null);

        return convertToOrderDetailDto(updatedRefund.getOriginalOrder());
    }

    private void createNewOrderFromPartialReturn(Order originalOrder, OrderRefund refund, UserAccount currentUser) {
        Order newOrder = new Order();
        newOrder.setOrderNumber(generateOrderNumber(originalOrder.getOrderTypeEnum()));
        newOrder.setOrderTypeCode(originalOrder.getOrderTypeCode());
        newOrder.setCompanyDivisionCode(originalOrder.getCompanyDivisionCode());
        newOrder.setStore(originalOrder.getStore());
        newOrder.setDistributor(originalOrder.getDistributor());
        newOrder.setUserAccount(currentUser); // The user who approved the return creates the new draft
        newOrder.setMember(originalOrder.getMember());
        newOrder.setCustomerName(originalOrder.getCustomerName());
        newOrder.setCustomerPhone(originalOrder.getCustomerPhone());
        newOrder.setOrderStatusCode(OrderStatusEnum.DRAFT.getCode());

        newOrder.setRemarks("此訂單由部分退貨自動產生，原訂單號: " + originalOrder.getOrderNumber() + ", 退貨單號: " + refund.getRefundOrderNumber());

        List<UUID> returnedItemIds = refund.getItems().stream()
                .map(item -> item.getOriginalOrderItem().getOrderItemId())
                .collect(Collectors.toList());

        List<OrderItem> newItems = originalOrder.getItems().stream()
                .filter(originalItem -> !returnedItemIds.contains(originalItem.getOrderItemId()))
                .map(originalItem -> {
                    OrderItem newItem = new OrderItem();
                    BeanUtils.copyProperties(originalItem, newItem, "id", "orderItemId", "order", "createTime", "updateTime", "createBy", "updateBy");
                    newItem.setOrder(newOrder);
                    return newItem;
                })
                .collect(Collectors.toList());

        newOrder.setItems(newItems);

        // Recalculate totals
        updateOrderTotals(newOrder);

        Order savedNewOrder = orderRepository.save(newOrder);
        orderChangeLogService.logOrderChange(savedNewOrder, null, savedNewOrder.getOrderStatusCode(), currentUser, "部分退貨後自動建立新訂單", null);

        // Update original refund record with the new order reference
        refund.setNewOrderId(savedNewOrder.getOrderId());
        orderRefundRepository.save(refund);
    }

    private void updateOrderTotals(Order order) {
        BigDecimal itemsSubtotalNet = BigDecimal.ZERO;
        BigDecimal itemsSubtotalGross = BigDecimal.ZERO;

        if (order.getItems() != null) {
            for (OrderItem item : order.getItems()) {
                itemsSubtotalGross = itemsSubtotalGross.add(item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())));
                itemsSubtotalNet = itemsSubtotalNet.add(item.getSubtotalAmount());
            }
        }
        order.setProductsTotalAmount(itemsSubtotalGross);
        order.setNetAmount(itemsSubtotalNet);
        order.setDiscountAmount(itemsSubtotalGross.subtract(itemsSubtotalNet));

        // 根據發票類型決定如何計算稅金
        BigDecimal taxAmount = BigDecimal.ZERO;
        if (order.getInvoiceTypeCode() != null && 
            (order.getInvoiceTypeCode().equals(InvoiceTypeEnum.TWO_PART.getCode()) || order.getInvoiceTypeCode().equals(InvoiceTypeEnum.THREE_PART.getCode()))) {
            BigDecimal taxRate = new BigDecimal("0.05"); // 假設稅率為 5%
            taxAmount = order.getNetAmount().multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
        }

        order.setTaxAmount(taxAmount);
        order.setGrandTotalAmount(order.getNetAmount().add(taxAmount));
    }

    private boolean checkIfPartialReturn(OrderRefund refund) {
        Order originalOrder = refund.getOriginalOrder();
        int originalItemCount = originalOrder.getItems().size();
        int returnedItemCount = refund.getItems().size();
        if(originalItemCount != returnedItemCount) return true;

        for(OrderRefundItem refundItem : refund.getItems()){
            if(!refundItem.getQuantity().equals(refundItem.getOriginalOrderItem().getQuantity())){
                return true;
            }
        }
        return false;
    }

    @Override
    public OrderDetailDto rejectOrderReturn(UUID refundId, String reason) {
        OrderRefund refund = orderRefundRepository.findById(refundId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到退貨單，ID: " + refundId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = refund.getRefundStatusCode();
        if (!Objects.equals(originalStatus, OrderStatusEnum.RETURN_PENDING_HQ_APPROVAL.getCode())) {
            throw new BadRequestException("退貨單狀態不正確，無法駁回。");
        }
        if (!StringUtils.hasText(reason)) {
            throw new BadRequestException("駁回退貨單必須提供原因。");
        }

        Short newStatus = OrderStatusEnum.RETURN_REJECTED_BY_HQ.getCode();
        refund.setRefundStatusCode(newStatus);

        Order originalOrder = refund.getOriginalOrder();
        OrderRefund updatedRefund = orderRefundRepository.save(refund);
        orderChangeLogService.logRefundChange(updatedRefund, originalStatus, newStatus, currentUser, "總公司駁回退貨", reason);

        return convertToOrderDetailDto(originalOrder);
    }

    // Helper method to reduce code duplication for simple status updates
    private OrderDetailDto updateOrderStatus(UUID orderId, OrderStatusEnum expectedOriginalStatus, OrderStatusEnum newStatus, String logReason, String reasonDetail) {
        return updateOrderStatusWithCallback(orderId, expectedOriginalStatus, newStatus, logReason, reasonDetail, null);
    }

    @Override
    public OrderDetailDto submitOrder(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        if (!order.getOrderStatusCode().equals(OrderStatusEnum.DRAFT.getCode())) {
            throw new BadRequestException("只有草稿狀態的訂單才能送出。");
        }

        // TODO: Implement actual price authority check logic here
        boolean priceExceedsAuthority = false; // Placeholder

        Short originalStatus = order.getOrderStatusCode();
        Short newStatus;

        // For Store Product Orders, the status remains DRAFT. 
        // The actual state transition happens on the detail page via checkoutStoreOrder.
        if (order.getOrderTypeCode().equals(OrderTypeEnum.STORE_PRODUCT_ORDER.getCode())) {
            newStatus = OrderStatusEnum.DRAFT.getCode();
        } else if (order.getOrderTypeCode().equals(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode())){
             // For Dispatch/Wholesale orders, check price authority
            if (priceExceedsAuthority) {
                newStatus = OrderStatusEnum.PENDING_STORE_APPROVAL.getCode(); // 20
            } else {
                // If price is within authority, it goes directly to store checkout complete
                newStatus = OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode(); // 30
            }
        }
        else {
             // For Wholesale orders and any other types, check price authority
            if (priceExceedsAuthority) {
                newStatus = OrderStatusEnum.PENDING_STORE_APPROVAL.getCode(); // 20
            } else {
                newStatus = OrderStatusEnum.PENDING_HQ_APPROVAL.getCode(); // 40
            }
        }

        order.setOrderStatusCode(newStatus);
        Order updatedOrder = orderRepository.save(order);

        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "訂單送出", null);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public void deleteDraftOrder(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        if (!order.getOrderStatusCode().equals(OrderStatusEnum.DRAFT.getCode())) {
            throw new BadRequestException("只有草稿狀態的訂單才能被刪除。");
        }

        order.setIsDeleted((short) 1);
        orderRepository.save(order);

        auditLogService.logAction(
            AuditActionTypeEnum.DELETE,
            AuditDataTypeEnum.ORDER,
            orderId.toString(),
            "刪除草稿訂單: " + order.getOrderNumber(),
            null
        );
    }

    @Override
    @Transactional
    public OrderDetailDto updateDispatchOrder(UUID orderId, DispatchProductOrderRequestDto dispatchDto) {
        // This method can now delegate to the more robust updateOrderForCorrection method.
        // First, ensure the order exists and is of the correct type.
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0 && o.getOrderTypeCode().equals(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode()))
            .orElseThrow(() -> new ResourceNotFoundException("找不到要修改的派工訂單，ID: " + orderId));

        // The DTO for correction is the base type, so this dispatchDto can be passed directly.
        return updateOrderForCorrection(orderId, dispatchDto);
    }

    @Override
    @Transactional
    public OrderDetailDto submitDispatchOrderFromForm(UUID orderId, DispatchProductOrderRequestDto requestDto) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到待修正的訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatusCode = order.getOrderStatusCode();

        // 1. Update order data (logic from updateOrderForCorrection)
        BeanUtils.copyProperties(requestDto, order, "items", "paymentDetails", "orderId", "orderNumber", "orderDate", "createTime", "updateTime", "createBy", "updateBy", "orderStatusCode");

        // --- START: Fix for missing technicianId ---
        if (requestDto.getTechnicianId() != null) {
            UserAccount technician = userAccountRepository.findById(requestDto.getTechnicianId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到技師，ID: " + requestDto.getTechnicianId()));
            order.setTechnician(technician);
        } else {
            order.setTechnician(null); // Explicitly set to null if not provided
        }
        // --- END: Fix for missing technicianId ---

        Customer customer = resolveCustomer(requestDto, currentUser);
        order.setMember(customer);
        if (customer != null) {
            order.setCustomerName(customer.getCustomerName());
            order.setCustomerPhone(customer.getPhoneNumber());
        }
        order.getItems().clear(); 
        List<OrderItem> allOrderItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(requestDto.getItems())) {
            for (OrderItemRequestDto itemDto : requestDto.getItems()) {
                OrderItem mainItem = createOrderItemFromDto(itemDto, order);
                //log.info("submitDispatchOrderFromForm 商品倉庫代碼：{}: {}", mainItem.getProductName(), mainItem.getWarehouseCode()); //--> 注意這裡傳入的已是 warehouseId

                if (!CollectionUtils.isEmpty(itemDto.getItemGroups())) {
                    List<OrderItemGroup> itemGroups = new ArrayList<>();
                    for (OrderItemGroupRequestDto groupDto : itemDto.getItemGroups()) {
                        OrderItemGroup group = new OrderItemGroup();
                        group.setOrderItem(mainItem);
                        group.setProductBarcode(groupDto.getProductBarcode());
                        group.setProductName(groupDto.getProductName());
                        group.setWarehouseId(groupDto.getWarehouseId());
                        group.setRequiresDispatch(groupDto.getRequiresDispatch());
                        group.setAwaitingMaterials(groupDto.getAwaitingMaterials());
                        group.setItemTypeCode(groupDto.getItemTypeCode());
                        group.setQuantity(groupDto.getQuantity());
                        group.setUnitPrice(groupDto.getUnitPrice());
                        group.setListPrice(groupDto.getListPrice());
                        group.setSubtotalAmount(groupDto.getUnitPrice().multiply(BigDecimal.valueOf(groupDto.getQuantity())));
                        group.setIsAwait(groupDto.getIsAwait() != null ? groupDto.getIsAwait() : 0);
                        itemGroups.add(group);
                    }
                    mainItem.setItemGroups(itemGroups);
                }
                allOrderItems.add(mainItem);
            }
        }
        order.getItems().addAll(allOrderItems);
        recalculateOrderTotals(order);
        processAndSavePayments(order, requestDto.getPaymentDetails(), currentUser);

        // 2. Transition status (logic from submitOrder)
        // Only transition status if the order is currently a draft
        if (originalStatusCode.equals(OrderStatusEnum.DRAFT.getCode())) {
            // If the request DTO specifies a status, it's a "Save Draft" action.
            if (requestDto.getOrderStatusCode() != null && requestDto.getOrderStatusCode().equals(OrderStatusEnum.DRAFT.getCode())) {
                order.setOrderStatusCode(OrderStatusEnum.DRAFT.getCode());
            } else {
                // Otherwise, it's a "Submit" action for a new or draft dispatch order
                // TODO: Implement actual price authority check logic here
                boolean priceExceedsAuthority = false;
                Short newStatus;
                if (priceExceedsAuthority) {
                    newStatus = OrderStatusEnum.PENDING_STORE_APPROVAL.getCode(); // 20
                } else {
                    newStatus = OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode(); // 30
                }
                order.setOrderStatusCode(newStatus);
            }
        } else if (originalStatusCode.equals(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode())) {
            // If the order is already at status 30, keep it at 30 upon saving.
            order.setOrderStatusCode(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode());
        }

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatusCode, updatedOrder.getOrderStatusCode(), currentUser, "派工訂單表單送出", requestDto);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    @Transactional
    public OrderDetailDto submitWholesaleOrderFromForm(UUID orderId, WholesaleProductOrderRequestDto requestDto) {
        Order order = orderRepository.findById(orderId)
                .filter(o -> o.getIsDeleted() == 0)
                .orElseThrow(() -> new ResourceNotFoundException("找不到待修正的訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatusCode = order.getOrderStatusCode();

        // 1. Update order data (logic from updateOrderForCorrection)
        BeanUtils.copyProperties(requestDto, order, "items", "paymentDetails", "orderId", "orderNumber", "orderDate", "createTime", "updateTime", "createBy", "updateBy", "orderStatusCode");

        // --- START: Fix for missing technicianId ---
        if (requestDto.getTechnicianId() != null) {
            UserAccount technician = userAccountRepository.findById(requestDto.getTechnicianId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到技師，ID: " + requestDto.getTechnicianId()));
            order.setTechnician(technician);
        } else {
            order.setTechnician(null); // Explicitly set to null if not provided
        }
        // --- END: Fix for missing technicianId ---

        Customer customer = resolveCustomer(requestDto, currentUser);
        order.setMember(customer);
        if (customer != null) {
            order.setCustomerName(customer.getCustomerName());
            order.setCustomerPhone(customer.getPhoneNumber());
        }
        order.getItems().clear();
        List<OrderItem> allOrderItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(requestDto.getItems())) {
            for (OrderItemRequestDto itemDto : requestDto.getItems()) {
                OrderItem mainItem = createOrderItemFromDto(itemDto, order);
                //log.info("submitDispatchOrderFromForm 商品倉庫代碼：{}: {}", mainItem.getProductName(), mainItem.getWarehouseCode()); //--> 注意這裡傳入的已是 warehouseId

                if (!CollectionUtils.isEmpty(itemDto.getItemGroups())) {
                    List<OrderItemGroup> itemGroups = new ArrayList<>();
                    for (OrderItemGroupRequestDto groupDto : itemDto.getItemGroups()) {
                        OrderItemGroup group = new OrderItemGroup();
                        group.setOrderItem(mainItem);
                        group.setProductBarcode(groupDto.getProductBarcode());
                        group.setProductName(groupDto.getProductName());
                        group.setWarehouseId(groupDto.getWarehouseId());
                        group.setRequiresDispatch(groupDto.getRequiresDispatch());
                        group.setAwaitingMaterials(groupDto.getAwaitingMaterials());
                        group.setItemTypeCode(groupDto.getItemTypeCode());
                        group.setQuantity(groupDto.getQuantity());
                        group.setUnitPrice(groupDto.getUnitPrice());
                        group.setListPrice(groupDto.getListPrice());
                        group.setSubtotalAmount(groupDto.getUnitPrice().multiply(BigDecimal.valueOf(groupDto.getQuantity())));
                        group.setIsAwait(groupDto.getIsAwait() != null ? groupDto.getIsAwait() : 0);
                        itemGroups.add(group);
                    }
                    mainItem.setItemGroups(itemGroups);
                }
                allOrderItems.add(mainItem);
            }
        }
        order.getItems().addAll(allOrderItems);
        recalculateOrderTotals(order);
        processAndSavePayments(order, requestDto.getPaymentDetails(), currentUser);

        // 2. Transition status (logic from submitOrder)
        // Only transition status if the order is currently a draft
        if (originalStatusCode.equals(OrderStatusEnum.DRAFT.getCode())) {
            // If the request DTO specifies a status, it's a "Save Draft" action.
            if (requestDto.getOrderStatusCode() != null && requestDto.getOrderStatusCode().equals(OrderStatusEnum.DRAFT.getCode())) {
                order.setOrderStatusCode(OrderStatusEnum.DRAFT.getCode());
            } else {
                // Otherwise, it's a "Submit" action for a new or draft dispatch order
                // TODO: Implement actual price authority check logic here
                boolean priceExceedsAuthority = false;
                Short newStatus;
                if (priceExceedsAuthority) {
                    newStatus = OrderStatusEnum.PENDING_STORE_APPROVAL.getCode(); // 20
                } else {
                    newStatus = OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode(); // 30
                }
                order.setOrderStatusCode(newStatus);
            }
        } else if (originalStatusCode.equals(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode())) {
            // If the order is already at status 30, keep it at 30 upon saving.
            order.setOrderStatusCode(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode());
        }

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatusCode, updatedOrder.getOrderStatusCode(), currentUser, "批發訂單表單送出", requestDto);

        return convertToOrderDetailDto(updatedOrder);
    }

    // --- DTO Conversion Helper Methods ---
    private OrderDetailDto convertToOrderDetailDto(Order order) {
        // Collect all warehouse IDs from the order to fetch them in one go
        Map<UUID, String> warehouseNameMap = new HashMap<>();
        if (order.getItems() != null) {
            Set<UUID> warehouseIds = new HashSet<>();
            order.getItems().forEach(item -> {
                if (item.getWarehouseCode() != null) {
                    try {
                        warehouseIds.add(UUID.fromString(item.getWarehouseCode()));
                    } catch (IllegalArgumentException e) {
                        // Ignore if warehouseCode is not a valid UUID
                    }
                }
                if (item.getItemGroups() != null) {
                    item.getItemGroups().forEach(group -> {
                        if (group.getWarehouseId() != null) {
                            warehouseIds.add(group.getWarehouseId());
                        }
                    });
                }
            });

            if (!warehouseIds.isEmpty()) {
                List<Warehouse> warehouses = warehouseRepository.findAllById(warehouseIds);
                warehouseNameMap.putAll(warehouses.stream()
                    .collect(Collectors.toMap(Warehouse::getWarehouseId, Warehouse::getWarehouseName)));
            }
        }

        OrderRefund latestRefund = orderRefundRepository.findLatestRefundsForOrders(Collections.singletonList(order.getOrderId()))
            .stream().findFirst().orElse(null);
        //log.debug("convertToOrderDetailDto orderId/latestRefund: {}/{}", order.getOrderId(), latestRefund != null ? latestRefund.getOrderRefundId() : "null");
        return this.convertToOrderDetailDto(order, latestRefund, warehouseNameMap);
    }

    private OrderDetailDto convertToOrderDetailDto(Order order, OrderRefund latestRefund, Map<UUID, String> warehouseNameMap) {
        OrderDetailDto dto = new OrderDetailDto();
        BeanUtils.copyProperties(order, dto, "items");

        // ** 權限檢查邏輯 **
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean canHoApprove = false;
        if (authentication != null) {
            canHoApprove = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch("F03011_DISPATCHAPPROVE"::equals);
        }
        dto.setCanApproveDispatch(canHoApprove);

        // ** 狀態判斷邏輯 **
        if (latestRefund != null && 
            latestRefund.getRefundStatusCode() != -46 && // CANCEL_REJECTED_BY_HQ
            latestRefund.getRefundStatusCode() != -77) { // RETURN_REJECTED_BY_HQ
            dto.setOrderStatusDescription(Optional.ofNullable(OrderStatusEnum.fromCode(latestRefund.getRefundStatusCode())).map(OrderStatusEnum::getDescription).orElse("未知退貨狀態"));
            dto.setOrderStatusCode(latestRefund.getRefundStatusCode());
        } else {
            dto.setOrderStatusDescription(Optional.ofNullable(OrderStatusEnum.fromCode(order.getOrderStatusCode())).map(OrderStatusEnum::getDescription).orElse("未知狀態"));
            // 這裡保持 dto.orderStatusCode 為原樣，因為 BeanUtils.copyProperties 已經複製過去了
        }

        if (order.getStore() != null) {
            dto.setStoreId(order.getStore().getStoreId());
            dto.setStoreName(order.getStore().getStoreName());
        }
        if (order.getUserAccount() != null) {
            dto.setUserAccountId(order.getUserAccount().getUserAccountId());
            dto.setUserEmployeeId(order.getUserAccount().getEmployeeId());
            dto.setUserName(order.getUserAccount().getUserName());
        }
        if (order.getMember() != null) {
            dto.setMemberId(order.getMember().getCustomerId());
            if (order.getMember().getMemberLevel() != null) {
                dto.setMemberLevelName(order.getMember().getMemberLevel().getLevelName());
            }
            dto.setCustomerName(order.getMember().getCustomerName());
            dto.setCustomerPhone(order.getMember().getPhoneNumber());
        } else {
            dto.setCustomerName(order.getCustomerName());
            dto.setCustomerPhone(order.getCustomerPhone());
        }
        dto.setCompanyDivisionDescription(Optional.ofNullable(ErpCompanyDivisionEnum.fromCode(order.getCompanyDivisionCode())).map(ErpCompanyDivisionEnum::getDescription).orElse(null));
        dto.setOrderTypeDescription(Optional.ofNullable(OrderTypeEnum.fromCode(order.getOrderTypeCode())).map(OrderTypeEnum::getDescription).orElse("未知類型"));
        dto.setOperatorTypeDescription(Optional.ofNullable(OrderOperatorTypeEnum.fromCode(order.getOperatorTypeCode())).map(OrderOperatorTypeEnum::getDescription).orElse(null));
        dto.setPaymentStatusDescription(Optional.ofNullable(PaymentStatusEnum.fromCode(order.getPaymentStatusCode())).map(PaymentStatusEnum::getDescription).orElse(null));
        dto.setInvoiceTypeDescription(Optional.ofNullable(InvoiceTypeEnum.fromCode(order.getInvoiceTypeCode())).map(InvoiceTypeEnum::getDescription).orElse(null));

        if (order.getTechnician() != null) {
            dto.setTechnicianId(order.getTechnician().getUserAccountId());
            dto.setTechnicianName(order.getTechnician().getUserName());
        }

        if (order.getSourceOrder() != null) {
            dto.setSourceOrderId(order.getSourceOrder().getOrderId());
            dto.setSourceOrderNumber(order.getSourceOrder().getOrderNumber());
        }

        if (!CollectionUtils.isEmpty(order.getItems())) {
            dto.setItems(order.getItems().stream()
                .map(item -> convertToOrderItemDto(item, warehouseNameMap))
                    .collect(Collectors.toList()));
        }

        dto.setIsDeleted(order.getIsDeleted() == 1);
        Optional.ofNullable(order.getCreateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> dto.setCreatedByName(user.getUserName()));
        Optional.ofNullable(order.getUpdateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> dto.setUpdatedByName(user.getUserName()));

        if (!CollectionUtils.isEmpty(order.getPayments())) {
            dto.setPaymentDetails(order.getPayments().stream()
                .filter(p -> p.getIsDeleted() == 0) // Filter for non-deleted payments
                .map(this::convertToOrderPaymentDto)
                .collect(Collectors.toList()));
        }

        // New logic for Dispatch Status
        if (!CollectionUtils.isEmpty(order.getDispatchRepairs())) {
            // Find the most recent dispatch/repair record and get its status
            order.getDispatchRepairs().stream()
                .max(Comparator.comparing(DispatchRepair::getCreateTime))
                .ifPresent(latestDispatch -> {
                    dto.setDispatchStatusDescription(DispatchStatusEnum.getDescriptionByCode(latestDispatch.getStatusCode()));
                });
        } else {
            dto.setDispatchStatusDescription("未派工");
        }

        if (order.getPromotion() != null) {
            dto.setPromotionId(order.getPromotion().getPromotionId());
            dto.setPromotionName(order.getPromotion().getPromotionName());
        }

        return dto;
    }

    private OrderItemDto convertToOrderItemDto(OrderItem item, Map<UUID, String> warehouseNameMap) {
        OrderItemDto itemDto = OrderItemDto.builder().build();
        BeanUtils.copyProperties(item, itemDto, "itemGroups");
        itemDto.setListPrice(item.getListPrice());
        itemDto.setItemTypeDescription(Optional.ofNullable(OrderItemTypeEnum.fromCode(item.getItemTypeCode())).map(OrderItemTypeEnum::getDescription).orElse(null));

        if (item.getWarehouseCode() != null) {
            try {
                UUID warehouseId = UUID.fromString(item.getWarehouseCode());
                itemDto.setWarehouseName(warehouseNameMap.getOrDefault(warehouseId, "未知倉庫"));
            } catch (IllegalArgumentException e) {
                //itemDto.setWarehouseName("無效倉庫ID");
            }
        }

        if (!CollectionUtils.isEmpty(item.getItemGroups())) {
            itemDto.setItemGroups(item.getItemGroups().stream()
                .map(group -> convertItemGroupToDto(group, warehouseNameMap))
                .collect(Collectors.toList()));
        }

        Optional.ofNullable(item.getCreateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> itemDto.setCreatedByName(user.getUserName()));
        Optional.ofNullable(item.getUpdateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> itemDto.setUpdatedByName(user.getUserName()));
        return itemDto;
    }

    private OrderSummaryDto convertToOrderSummaryDto(Order order) {
        return this.convertToOrderSummaryDto(order, null);
    }

    private OrderSummaryDto convertToOrderSummaryDto(Order order, OrderRefund latestRefund) {
        OrderSummaryDto summaryDto = new OrderSummaryDto();
        BeanUtils.copyProperties(order, summaryDto, "createBy", "updateBy");

        // ** 狀態判斷邏輯 **
        if (latestRefund != null && 
            latestRefund.getRefundStatusCode() != -46 && 
            latestRefund.getRefundStatusCode() != -77) {
            summaryDto.setOrderStatusDescription(Optional.ofNullable(OrderStatusEnum.fromCode(latestRefund.getRefundStatusCode())).map(OrderStatusEnum::getDescription).orElse("未知退貨狀態"));
            summaryDto.setOrderStatusCode(latestRefund.getRefundStatusCode());
            summaryDto.setUpdateTime(latestRefund.getUpdateTime());
        } else {
            summaryDto.setOrderStatusDescription(Optional.ofNullable(OrderStatusEnum.fromCode(order.getOrderStatusCode())).map(OrderStatusEnum::getDescription).orElse("未知狀態"));
            // orderStatusCode is already copied by BeanUtils
        }

        summaryDto.setCompanyDivisionDescription(Optional.ofNullable(ErpCompanyDivisionEnum.fromCode(order.getCompanyDivisionCode())).map(ErpCompanyDivisionEnum::getDescription).orElse(null));
        summaryDto.setOrderTypeDescription(Optional.ofNullable(OrderTypeEnum.fromCode(order.getOrderTypeCode())).map(OrderTypeEnum::getDescription).orElse("未知類型"));

        if(order.getStore() != null) {
            summaryDto.setStoreName(order.getStore().getStoreName());
        }

        if (!CollectionUtils.isEmpty(order.getItems())) {
            order.getItems().stream()
                .filter(it -> OrderItemTypeEnum.MAIN_PRODUCT.getCode().equals(it.getItemTypeCode()))
                .findFirst()
                .ifPresent(mainItem -> summaryDto.setMainProductName(mainItem.getProductName()));
        }

        Optional.ofNullable(order.getUpdateBy()).flatMap(userAccountRepository::findById).ifPresent(u -> summaryDto.setUpdatedByName(u.getUserName()));
        return summaryDto;
    }

    private OrderPaymentDto convertToOrderPaymentDto(OrderPayment payment) {
        if (payment == null) return null;
        OrderPaymentDto dto = new OrderPaymentDto();
        BeanUtils.copyProperties(payment, dto, "paymentMethodCode");
        dto.setPaymentMethodCode(PaymentMethodEnum.valueOf(payment.getPaymentMethodCode()));
        // cardBrand 和 cardIssuer 會被 BeanUtils 自動複製，因為名稱相同
        return dto;
    }

    @Override
    public OrderDetailDto copyOrderToNew(UUID orderId) {
        Order originalOrder = orderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("找不到要複製的訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Order newOrder = new Order();
        BeanUtils.copyProperties(originalOrder, newOrder, 
            "id", "orderId", "orderNumber", "orderDate", "createTime", "updateTime", "createBy", "updateBy", 
            "items", "paymentStatusCode", "paidAmount", "orderStatusCode");

        newOrder.setOrderStatusCode(OrderStatusEnum.DRAFT.getCode());
        newOrder.setPaymentStatusCode(PaymentStatusEnum.UNPAID.getCode());
        newOrder.setPaidAmount(BigDecimal.ZERO);
        newOrder.setOrderNumber(generateOrderNumber(OrderTypeEnum.fromCode(originalOrder.getOrderTypeCode())));
        newOrder.setRemarks("複製自訂單: " + originalOrder.getOrderNumber());
        newOrder.setUserAccount(currentUser);

        List<OrderItem> newItems = originalOrder.getItems().stream().map(originalItem -> {
            OrderItem newItem = new OrderItem();
            BeanUtils.copyProperties(originalItem, newItem, "id", "orderItemId", "order", "createTime", "updateTime", "createBy", "updateBy");
            newItem.setOrder(newOrder);
            return newItem;
        }).collect(Collectors.toList());

        newOrder.setItems(newItems);

        Order savedNewOrder = orderRepository.save(newOrder);
        orderChangeLogService.logOrderChange(savedNewOrder, null, savedNewOrder.getOrderStatusCode(), currentUser, "複製訂單", originalOrder.getOrderNumber());

        return convertToOrderDetailDto(savedNewOrder);
    }

    //門市商品訂單退換貨
    @Override
    @Transactional
    public OrderRefundDto initiateOrderReturnOrChange(UUID orderId, OrderActionTypeEnum actionType) {
        Order order = orderRepository.findById(orderId)
                .filter(o -> o.getIsDeleted() == 0)
                .orElseThrow(() -> new ResourceNotFoundException("找不到要退/換貨的訂單，ID: " + orderId));

        if (!order.getOrderStatusCode().equals(OrderStatusEnum.SHIPPED_CLOSED.getCode())) {
            throw new BadRequestException("只有已出貨結案的訂單才能申請退/換貨。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        OrderRefund refund = new OrderRefund();
        refund.setOrderRefundId(UUID.randomUUID());
        refund.setOriginalOrder(order);
        refund.setRefundOrderNumber("R" + order.getOrderNumber());
        refund.setCompanyDivisionCode(order.getCompanyDivisionCode());
        refund.setRefundTypeCode(actionType.getCode());
        refund.setRefundStatusCode(OrderStatusEnum.RETURN_APPROVED_ITEM_RECEIVED.getCode()); // 退貨成立已取貨
        refund.setRequestDate(OffsetDateTime.now());
        refund.setRequestByUser(currentUser); // 設定申請人實體

        // Map OrderItems to OrderRefundItems
        List<OrderRefundItem> refundItems = order.getItems().stream().map(originalItem -> {
            OrderRefundItem refundItem = new OrderRefundItem();
            BeanUtils.copyProperties(originalItem, refundItem, "orderItemId", "order", "itemGroups");
            refundItem.setOrderRefundItemId(UUID.randomUUID());
            refundItem.setOriginalOrderItem(originalItem);
            refundItem.setOrderRefund(refund);
            if (OrderActionTypeEnum.CHANGE==actionType) {
                refundItem.setRefundAmountPerItem(BigDecimal.ZERO);
                refundItem.setSubtotalRefundAmount(BigDecimal.ZERO);
            } else {
                refundItem.setRefundAmountPerItem(originalItem.getFinalPricePerItem());
                refundItem.setSubtotalRefundAmount(originalItem.getSubtotalAmount());
            }
            return refundItem;
        }).collect(Collectors.toList());

        refund.setItems(refundItems);
        refund.setExpectedRefundAmount(order.getGrandTotalAmount());

        OrderRefund savedRefund = orderRefundRepository.save(refund);

        OrderRefundDto dto = new OrderRefundDto();
        dto.setRefundId(savedRefund.getOrderRefundId());
        dto.setOriginalOrderId(orderId);
        dto.setRefundOrderNumber(savedRefund.getRefundOrderNumber());
        dto.setRefundStatusCode(savedRefund.getRefundStatusCode());
        dto.setRefundStatusDescription("退貨成立已取貨");
        return dto;
    }

    //派工商品訂單退換貨
    @Override
    @Transactional
    public OrderRefundDto initiateDispatchOrderReturnOrChange(UUID orderId, OrderActionTypeEnum actionType) {
        //log.info("call initiateDispatchOrderReturnOrChange");
        Order order = orderRepository.findById(orderId)
                .filter(o -> o.getIsDeleted() == 0)
                .orElseThrow(() -> new ResourceNotFoundException("找不到要退/換貨的訂單，ID: " + orderId));

        //log.info("order.getOrderStatusCode(): {}", order.getOrderStatusCode());
        if (OrderStatusEnum.HQ_APPROVED.getCode()>order.getOrderStatusCode()) {
            throw new BadRequestException("只有已送總公司審核的訂單才能申請退/換貨。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        //log.info("create new refund");
        OrderRefund refund = new OrderRefund();
        refund.setOrderRefundId(UUID.randomUUID());
        refund.setOriginalOrder(order);
        refund.setRefundOrderNumber("R" + order.getOrderNumber());
        refund.setCompanyDivisionCode(order.getCompanyDivisionCode());
        refund.setRefundTypeCode(actionType.getCode());
        refund.setRequestDate(OffsetDateTime.now());
        refund.setRequestByUser(currentUser); // 設定申請人實體

        // Map OrderItems to OrderRefundItems
        //log.info("create new refundItem");
        List<OrderRefundItem> refundItems = order.getItems().stream().map(originalItem -> {
            OrderRefundItem refundItem = new OrderRefundItem();
            BeanUtils.copyProperties(originalItem, refundItem, "orderItemId", "order", "itemGroups");
            refundItem.setOrderRefundItemId(UUID.randomUUID());
            refundItem.setOriginalOrderItem(originalItem);
            refundItem.setOrderRefund(refund);
            if (OrderActionTypeEnum.CHANGE==actionType) {
                refundItem.setRefundAmountPerItem(BigDecimal.ZERO);
                refundItem.setSubtotalRefundAmount(BigDecimal.ZERO);
            } else {
                refundItem.setRefundAmountPerItem(originalItem.getFinalPricePerItem());
                refundItem.setSubtotalRefundAmount(originalItem.getSubtotalAmount());
            }

            // --- START: New logic to copy item groups ---
            //log.info("create new refundItemGroup");
            if (!CollectionUtils.isEmpty(originalItem.getItemGroups())) {
                List<OrderRefundItemGroup> refundItemGroups = originalItem.getItemGroups().stream().map(originalGroup -> {
                    OrderRefundItemGroup refundGroup = new OrderRefundItemGroup();
                    BeanUtils.copyProperties(originalGroup, refundGroup, "orderItemGroupId", "orderItem");
                    refundGroup.setOrderRefundItemGroupId(UUID.randomUUID());
                    refundGroup.setOriginalOrderItemGroupId(originalGroup.getOrderItemGroupId());
                    refundGroup.setOrderRefundItem(refundItem); // Associate with the new refund item
                    if (OrderActionTypeEnum.CHANGE==actionType) {
                        refundGroup.setSubtotalRefundAmount(BigDecimal.ZERO);
                    } else {
                        refundGroup.setSubtotalRefundAmount(originalGroup.getSubtotalAmount());
                    }
                    return refundGroup;
                }).collect(Collectors.toList());
                refundItem.setItemGroups(refundItemGroups);
            }
            // --- END: New logic to copy item groups ---

            return refundItem;
        }).collect(Collectors.toList());

        refund.setItems(refundItems);
        refund.setExpectedRefundAmount(order.getGrandTotalAmount());

        String refundStatusDescription;

        //依不同派工狀態進行退機流程
        // 1. 檢查此訂單是否已產生派工單(sm_dispatch_repair 的 order_id 有此訂單編號)。
        Optional<DispatchRepair> latestDispatchRepairOpt = order.getDispatchRepairs().stream()
                .max(Comparator.comparing(DispatchRepair::getCreateTime));

        // 2. 如果尚未產生派工單，則訂單狀態設為 OrderStatusEnum.RETURN_APPROVED_ITEM_RECEIVED(-88)。
        if (latestDispatchRepairOpt.isEmpty()) {
            //log.info("had no DispatchRepairs");
            refund.setRefundStatusCode(OrderStatusEnum.RETURN_APPROVED_ITEM_RECEIVED.getCode());
        } else {
            DispatchRepair dispatchRepair = latestDispatchRepairOpt.get();
            short dispatchStatus = dispatchRepair.getStatusCode();

            // 3. 如果已產生派工單：
            if (dispatchStatus >= DispatchStatusEnum.PENDING_ASSIGNMENT.getCode() &&
                dispatchStatus < DispatchStatusEnum.MATERIALS_COLLECTED.getCode()) {
                // 如果派工單狀態 大於等於 PENDING_ASSIGNMENT(20) 且小於 MATERIALS_COLLECTED(35), 則訂單狀態設為 OrderStatusEnum.RETURN_APPROVED_ITEM_RECEIVED(-86), 派工單單狀態則改為 REFUND_ACCOUNT_CONFIRM(-99)
                refund.setRefundStatusCode(OrderStatusEnum.RETURN_APPROVED_ITEM_RECEIVED.getCode());
                dispatchRepair.setStatusCode(DispatchStatusEnum.REFUND_ACCOUNT_CONFIRM.getCode());
                dispatchRepairRepository.save(dispatchRepair);
            } else if (dispatchStatus == DispatchStatusEnum.MATERIALS_COLLECTED.getCode()) {
                // 如果派工單狀態 等於 MATERIALS_COLLECTED(35), 則訂單狀態設為 OrderStatusEnum.RETURN_APPROVED_ITEM_RECEIVED(-86), 派工單單狀態則改為 REFUND_MATERIALS_CANCELED(-85)
                refund.setRefundStatusCode(OrderStatusEnum.RETURN_APPROVED_ITEM_RECEIVED.getCode());
                dispatchRepair.setStatusCode(DispatchStatusEnum.REFUND_MATERIALS_BACK.getCode());
                dispatchRepairRepository.save(dispatchRepair);
            } else if (dispatchStatus >= DispatchStatusEnum.DEPARTED.getCode()) {
                // 如果派工單狀態 大於等於 DEPARTED(40), 則訂單狀態設為 OrderStatusEnum.RETURN_APPROVED_PENDING_PICKUP(-83)
                refund.setRefundStatusCode(OrderStatusEnum.RETURN_APPROVED_PENDING_PICKUP.getCode());

                // 當派工單狀態 大於等於 DEPARTED(40) 且小於等於 PROCESS_INFO(80) 則派工單狀態改為 DispatchStatusEnum code 乘以 -1
                if (dispatchStatus <= DispatchStatusEnum.PROCESS_INFO.getCode()) {
                    dispatchRepair.setStatusCode((short) (dispatchStatus * -1));
                    dispatchRepairRepository.save(dispatchRepair);
                } else {
                    // 如果派工單狀態已 大於等於 ACCOUNT_CONFIRM(90)，則要重新將訂單商品資訊複製過來建立一筆派工(退機)單
                    dispatchRepairService.createDispatchRepairsFromOrder(order.getOrderId(), DispatchRepairTypeEnum.RETURN, DispatchStatusEnum.REFUND_PENDING_ASSIGNMENT);
                }
            } else {
                 // 如果派工單狀態不在上述範圍內，則維持原退貨單狀態
                 refund.setRefundStatusCode(OrderStatusEnum.RETURN_APPROVED_PENDING_PICKUP.getCode());
            }
            //log.info("had check DispatchRepairs: {}", dispatchRepair.getStatusCode());
        }

        OrderRefund savedRefund = orderRefundRepository.save(refund);
        refundStatusDescription = Optional.ofNullable(OrderStatusEnum.fromCode(savedRefund.getRefundStatusCode()))
                .map(OrderStatusEnum::getDescription).orElse("未知狀態");

        OrderRefundDto dto = new OrderRefundDto();
        dto.setRefundId(savedRefund.getOrderRefundId());
        dto.setOriginalOrderId(orderId);
        dto.setRefundOrderNumber(savedRefund.getRefundOrderNumber());
        dto.setRefundStatusCode(savedRefund.getRefundStatusCode());
        dto.setRefundStatusDescription(refundStatusDescription);
        return dto;
    }

    @Override
    @Transactional
    public Object completeStoreReturn(UUID refundId, StoreReturnRequestDto requestDto) {
        OrderRefund refund = orderRefundRepository.findById(refundId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到退貨單，ID: " + refundId));

        if (refund.getRefundStatusCode() != -86) { // -86: 退貨成立已取貨
            throw new BadRequestException("退貨單狀態不正確，無法完成。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Order originalOrder = refund.getOriginalOrder();

        // 根據是「退貨」還是「換貨」執行不同邏輯
        if (refund.getRefundTypeCode().equals(OrderActionTypeEnum.RETURN.getCode())) {
            refund.setActualRefundAmount(requestDto.getRefundAmount());
            refund.setReason(requestDto.getReason());
            refund.setActualRefundDate(OffsetDateTime.now());
            refund.setRefundStatusCode(OrderStatusEnum.STORE_REFUND_CHECKOUT_COMPLETE.getCode());

            restockInventoryForStoreReturn(originalOrder);
            createJournalEntryForRefund(originalOrder, refund, currentUser);

            orderRefundRepository.save(refund);
            return convertToOrderDetailDto(originalOrder);
        } else if (refund.getRefundTypeCode().equals(OrderActionTypeEnum.CHANGE.getCode())) {
            refund.setActualRefundAmount(BigDecimal.ZERO); // 換貨時，實際退款金額強制為0
            refund.setReason(requestDto.getReason());
            refund.setActualRefundDate(OffsetDateTime.now());
            refund.setRefundStatusCode(OrderStatusEnum.STORE_REFUND_CHANGE.getCode());

            restockInventoryForStoreReturn(originalOrder);
            createJournalEntryForRefund(originalOrder, refund, currentUser); // 日記帳中支出為0

            orderRefundRepository.save(refund);

            Order newOrder = copyOrderForChange(originalOrder, currentUser);
            return new ChangeOrderResponseDto(newOrder.getOrderId());
        } else {
            throw new BadRequestException("未知的退貨單類型。");
        }
    }

    @Override
    @Transactional
    public Object completeDispatchReturn(UUID refundId, StoreReturnRequestDto requestDto) {
        //todo:未完成,換貨需改為派工商品邏輯(or 無換貨？)
        OrderRefund refund = orderRefundRepository.findById(refundId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到退貨單，ID: " + refundId));

        if (refund.getRefundStatusCode() != -86) { // -86: 退貨成立已取貨
            throw new BadRequestException("退貨單狀態不正確，無法完成。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Order originalOrder = refund.getOriginalOrder();

        // 根據是「退貨」還是「換貨」執行不同邏輯
        if (refund.getRefundTypeCode().equals(OrderActionTypeEnum.RETURN.getCode())) {
            refund.setActualRefundAmount(requestDto.getRefundAmount());
            refund.setReason(requestDto.getReason());
            refund.setActualRefundDate(OffsetDateTime.now());
            refund.setRefundStatusCode(OrderStatusEnum.RETURN_PENDING_HQ_APPROVAL.getCode());

            restockInventoryForStoreReturn(originalOrder);
            createJournalEntryForRefund(originalOrder, refund, currentUser);

            orderRefundRepository.save(refund);
            return convertToOrderDetailDto(originalOrder);
        } else if (refund.getRefundTypeCode().equals(OrderActionTypeEnum.CHANGE.getCode())) {
            refund.setActualRefundAmount(BigDecimal.ZERO); // 換貨時，實際退款金額強制為0
            refund.setReason(requestDto.getReason());
            refund.setActualRefundDate(OffsetDateTime.now());
            refund.setRefundStatusCode(OrderStatusEnum.STORE_REFUND_CHANGE.getCode());

            restockInventoryForStoreReturn(originalOrder);
            createJournalEntryForRefund(originalOrder, refund, currentUser); // 日記帳中支出為0

            orderRefundRepository.save(refund);

            Order newOrder = copyOrderForDispatchChange(originalOrder, currentUser);
            return new ChangeOrderResponseDto(newOrder.getOrderId());
        } else {
            throw new BadRequestException("未知的退貨單類型。");
        }
    }

    private void createJournalEntryForRefund(Order order, OrderRefund refund, UserAccount currentUser) {
        if (order.getStore() == null) return;
        // ... (寫入日記帳的邏輯)
    }

    private void restockInventoryForStoreReturn(Order order) {
        // ... (回補庫存的邏輯)
    }

    private Order copyOrderForChange(Order originalOrder, UserAccount currentUser) {
        Order newOrder = new Order();
        BeanUtils.copyProperties(originalOrder, newOrder, "id", "orderId", "orderNumber", "orderDate", "createTime", "updateTime", "createBy", "updateBy", "items", "payments", "orderStatusCode", "dispatchRepairs");

        newOrder.setOrderNumber(generateOrderNumber(OrderTypeEnum.STORE_PRODUCT_ORDER));
        newOrder.setOrderStatusCode(OrderStatusEnum.DRAFT.getCode());

        BigDecimal transferredAmount = originalOrder.getGrandTotalAmount();
        newOrder.setPaidAmount(transferredAmount); 

        // 根據已付金額更新付款狀態
        if (transferredAmount != null && transferredAmount.compareTo(BigDecimal.ZERO) > 0) {
             newOrder.setPaymentStatusCode(
                transferredAmount.compareTo(newOrder.getGrandTotalAmount()) >= 0 ? 
                PaymentStatusEnum.FULLY_PAID.getCode() : 
                PaymentStatusEnum.PARTIALLY_PAID.getCode()
            );
        } else {
            newOrder.setPaymentStatusCode(PaymentStatusEnum.UNPAID.getCode());
        }

        // 為換貨轉入的金額建立一筆對應的 OrderPayment 記錄
        OrderPayment exchangePayment = new OrderPayment();
        exchangePayment.setOrder(newOrder);
        exchangePayment.setPaymentMethodCode(PaymentMethodEnum.EXCHANGE.name());
        exchangePayment.setAmount(transferredAmount);
        exchangePayment.setPaymentTime(OffsetDateTime.now());
        exchangePayment.setRemarks("由換貨流程自動產生");

        List<OrderPayment> payments = new ArrayList<>();
        payments.add(exchangePayment);
        newOrder.setPayments(payments);

        newOrder.setRemarks("由換貨流程自動產生，原訂單: " + originalOrder.getOrderNumber() + 
                            ", 原單總額: " + originalOrder.getNetAmount() + ", 原單含稅總額: " + originalOrder.getGrandTotalAmount());
        newOrder.setUserAccount(currentUser);

        List<OrderItem> newItems = originalOrder.getItems().stream().map(oldItem -> {
            OrderItem newItem = new OrderItem();
            BeanUtils.copyProperties(oldItem, newItem, "id", "orderItemId", "order", "itemGroups", "originalGiftTotal", "exchangedGiftTotal");
            newItem.setOrder(newOrder);
            return newItem;
        }).collect(Collectors.toList());
        newOrder.setItems(newItems);

        return orderRepository.save(newOrder);
    }

    private Order copyOrderForDispatchChange(Order originalOrder, UserAccount currentUser) {
        Order newOrder = new Order();
        BeanUtils.copyProperties(originalOrder, newOrder, "id", "orderId", "orderNumber", "orderDate", "createTime", "updateTime", "createBy", "updateBy", "items", "payments", "orderStatusCode", "dispatchRepairs");

        newOrder.setOrderNumber(generateOrderNumber(OrderTypeEnum.STORE_PRODUCT_ORDER));
        newOrder.setOrderStatusCode(OrderStatusEnum.DRAFT.getCode());

        BigDecimal transferredAmount = originalOrder.getGrandTotalAmount();
        newOrder.setPaidAmount(transferredAmount);

        // 根據已付金額更新付款狀態
        if (transferredAmount != null && transferredAmount.compareTo(BigDecimal.ZERO) > 0) {
            newOrder.setPaymentStatusCode(
                    transferredAmount.compareTo(newOrder.getGrandTotalAmount()) >= 0 ?
                            PaymentStatusEnum.FULLY_PAID.getCode() :
                            PaymentStatusEnum.PARTIALLY_PAID.getCode()
            );
        } else {
            newOrder.setPaymentStatusCode(PaymentStatusEnum.UNPAID.getCode());
        }

        // 為換貨轉入的金額建立一筆對應的 OrderPayment 記錄
        OrderPayment exchangePayment = new OrderPayment();
        exchangePayment.setOrder(newOrder);
        exchangePayment.setPaymentMethodCode(PaymentMethodEnum.EXCHANGE.name());
        exchangePayment.setAmount(transferredAmount);
        exchangePayment.setPaymentTime(OffsetDateTime.now());
        exchangePayment.setRemarks("由換貨流程自動產生");

        List<OrderPayment> payments = new ArrayList<>();
        payments.add(exchangePayment);
        newOrder.setPayments(payments);

        newOrder.setRemarks("由換貨流程自動產生，原訂單: " + originalOrder.getOrderNumber() +
                ", 原單總額: " + originalOrder.getNetAmount() + ", 原單含稅總額: " + originalOrder.getGrandTotalAmount());
        newOrder.setUserAccount(currentUser);

        List<OrderItem> newItems = originalOrder.getItems().stream().map(oldItem -> {
            OrderItem newItem = new OrderItem();
            BeanUtils.copyProperties(oldItem, newItem, "id", "orderItemId", "order", "itemGroups", "originalGiftTotal", "exchangedGiftTotal");
            newItem.setOrder(newOrder);
            return newItem;
        }).collect(Collectors.toList());
        newOrder.setItems(newItems);

        return orderRepository.save(newOrder);
    }

    private OrderItemGroupDto convertItemGroupToDto(OrderItemGroup group, Map<UUID, String> warehouseNameMap) {
        OrderItemGroupDto dto = new OrderItemGroupDto();
        BeanUtils.copyProperties(group, dto);

        if (group.getWarehouseId() != null) {
            dto.setWarehouseName(warehouseNameMap.getOrDefault(group.getWarehouseId(), "未知倉庫"));
        }

        return dto;
    }

    private OrderItem createOrderItemFromDto(OrderItemRequestDto itemDto, Order order) {
        OrderItem item = new OrderItem();
        // Use BeanUtils to copy all matching fields from DTO to entity
        BeanUtils.copyProperties(itemDto, item, "itemGroups");

        // Manually set fields that require special handling or are not in DTO
        item.setOrderItemId(UUID.randomUUID());
        item.setOrder(order);

        // Explicitly set productName to ensure it's captured from the DTO
        item.setProductName(itemDto.getProductName());

        // Set listPrice from DTO
        item.setListPrice(itemDto.getListPrice());

        // Recalculate financial fields on the backend for data integrity
        BigDecimal quantity = new BigDecimal(itemDto.getQuantity());
        BigDecimal unitPrice = Optional.ofNullable(itemDto.getUnitPrice()).orElse(BigDecimal.ZERO);
        BigDecimal discountAmount = Optional.ofNullable(itemDto.getDiscountAmountPerItem()).orElse(BigDecimal.ZERO);
        BigDecimal discountRate = Optional.ofNullable(itemDto.getDiscountRate()).orElse(BigDecimal.ZERO);

        BigDecimal priceAfterRateDiscount = unitPrice.multiply(BigDecimal.ONE.subtract(discountRate));
        item.setFinalPricePerItem(priceAfterRateDiscount.subtract(discountAmount).max(BigDecimal.ZERO));
        item.setSubtotalAmount(item.getFinalPricePerItem().multiply(quantity));

        return item;
    }

    @Override
    @Transactional(readOnly = true)
    public OrderDetailDto getDispatchOrderDetailById(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0 && o.getOrderTypeCode().equals(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode()))
            .orElseThrow(() -> new ResourceNotFoundException("找不到派工訂單，ID: " + orderId));

        // 獲取最新的退貨單
        OrderRefund latestRefund = orderRefundRepository.findLatestRefundsForOrders(Collections.singletonList(orderId))
                .stream().findFirst().orElse(null);
        //log.debug("getDispatchOrderDetailById orderId/latestRefund: {}/{}", order.getOrderId(), latestRefund);

        Map<UUID, String> warehouseNameMap = new HashMap<>();
        if (order.getItems() != null) {
            Set<UUID> warehouseIds = new HashSet<>();
        order.getItems().forEach(item -> {
                // Collect warehouseId from the item itself
                if (StringUtils.hasText(item.getWarehouseCode())) {
                    try {
                        warehouseIds.add(UUID.fromString(item.getWarehouseCode()));
                    } catch (IllegalArgumentException e) {
                        // Log or ignore if the code is not a valid UUID
                    }
                }
                // Collect warehouseIds from the item groups
                if (item.getItemGroups() != null) {
                    item.getItemGroups().forEach(group -> {
                        if (group.getWarehouseId() != null) {
                            warehouseIds.add(group.getWarehouseId());
                        }
                    });
                }
            });
            if (!warehouseIds.isEmpty()) {
                List<Warehouse> warehouses = warehouseRepository.findAllById(warehouseIds);
                warehouseNameMap.putAll(warehouses.stream()
                    .collect(Collectors.toMap(Warehouse::getWarehouseId, Warehouse::getWarehouseName)));
            }
        }

        return convertToOrderDetailDto(order, latestRefund, warehouseNameMap);
    }

    @Override
    @Transactional(readOnly = true)
    public OrderDetailDto getWholesaleOrderDetailById(UUID orderId) {
        Order order = orderRepository.findById(orderId)
                .filter(o -> o.getIsDeleted() == 0 && o.getOrderTypeCode().equals(OrderTypeEnum.WHOLESALE_ORDER.getCode()))
                .orElseThrow(() -> new ResourceNotFoundException("找不到批發訂單，ID: " + orderId));

        // 獲取最新的退貨單
        OrderRefund latestRefund = orderRefundRepository.findLatestRefundsForOrders(Collections.singletonList(orderId))
                .stream().findFirst().orElse(null);
        //log.debug("getDispatchOrderDetailById orderId/latestRefund: {}/{}", order.getOrderId(), latestRefund);

        Map<UUID, String> warehouseNameMap = new HashMap<>();
        if (order.getItems() != null) {
            Set<UUID> warehouseIds = new HashSet<>();
            order.getItems().forEach(item -> {
                // Collect warehouseId from the item itself
                if (StringUtils.hasText(item.getWarehouseCode())) {
                    try {
                        warehouseIds.add(UUID.fromString(item.getWarehouseCode()));
                    } catch (IllegalArgumentException e) {
                        // Log or ignore if the code is not a valid UUID
                    }
                }
                // Collect warehouseIds from the item groups
                if (item.getItemGroups() != null) {
                    item.getItemGroups().forEach(group -> {
                        if (group.getWarehouseId() != null) {
                            warehouseIds.add(group.getWarehouseId());
                        }
                    });
                }
            });
            if (!warehouseIds.isEmpty()) {
                List<Warehouse> warehouses = warehouseRepository.findAllById(warehouseIds);
                warehouseNameMap.putAll(warehouses.stream()
                        .collect(Collectors.toMap(Warehouse::getWarehouseId, Warehouse::getWarehouseName)));
            }
        }

        return convertToOrderDetailDto(order, latestRefund, warehouseNameMap);
    }

    @Override
    @Transactional
    public OrderDetailDto processDispatchOrderCheckout(UUID orderId) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0 && o.getOrderTypeCode().equals(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode()))
            .orElseThrow(() -> new ResourceNotFoundException("找不到要結帳的派工訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();

        if (!originalStatus.equals(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode())) {
            throw new BadRequestException("只有狀態為 '門市結帳' 的派工訂單才能執行結帳送出。");
        }

        // 寫入門市派工日記帳
        for (OrderPayment payment : order.getPayments()) {
             createJournalEntryForPayment(order, payment, currentUser);
        }

        boolean stockIsSufficient = inventoryService.checkAndReserveStock(order);

        Short newStatus;
        if (stockIsSufficient) {
            newStatus = OrderStatusEnum.PENDING_HQ_APPROVAL.getCode(); // 40
        } else {
            newStatus = OrderStatusEnum.HQ_STOCK_INSUFFICIENT.getCode(); // 36
        }

        order.setOrderStatusCode(newStatus);
        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "派工訂單結帳送出", null);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    @Transactional
    public OrderDetailDto processWholesaleOrderCheckout(UUID orderId) {
        Order order = orderRepository.findById(orderId)
                .filter(o -> o.getIsDeleted() == 0 && o.getOrderTypeCode().equals(OrderTypeEnum.WHOLESALE_ORDER.getCode()))
                .orElseThrow(() -> new ResourceNotFoundException("找不到要結帳的批發訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();

        if (!originalStatus.equals(OrderStatusEnum.STORE_CHECKOUT_COMPLETE.getCode())) {
            throw new BadRequestException("只有狀態為 '門市結帳' 的批發訂單才能執行結帳送出。");
        }

        // 寫入門市派工日記帳
        for (OrderPayment payment : order.getPayments()) {
            createJournalEntryForPayment(order, payment, currentUser);
        }

        boolean stockIsSufficient = inventoryService.checkAndReserveStock(order);

        Short newStatus;
        if (stockIsSufficient) {
            newStatus = OrderStatusEnum.PENDING_HQ_APPROVAL.getCode(); // 40
        } else {
            newStatus = OrderStatusEnum.HQ_STOCK_INSUFFICIENT.getCode(); // 36
        }

        order.setOrderStatusCode(newStatus);
        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus, currentUser, "批發訂單結帳送出", null);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    @Transactional
    public OrderDetailDto submitForHqApproval(UUID orderId) {
        return updateOrderStatus(orderId, OrderStatusEnum.HQ_STOCK_FULL, OrderStatusEnum.PENDING_HQ_APPROVAL, "已有庫存，送至總公司審核", null);
    }

    private OrderDetailDto updateOrderStatusWithCallback(UUID orderId, OrderStatusEnum expectedOriginalStatus, OrderStatusEnum newStatus, String logReason, String reasonDetail, Consumer<Order> callback) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();
        if (!originalStatus.equals(expectedOriginalStatus.getCode())) {
            throw new BadRequestException("訂單狀態不為 '" + expectedOriginalStatus.getDescription() + "'，無法執行此操作。");
        }

        order.setOrderStatusCode(newStatus.getCode());

        if (callback != null) {
            callback.accept(order);
        }

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, newStatus.getCode(), currentUser, logReason, reasonDetail);
        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    public void setItemAwaitingMaterial(UUID orderItemGroupId, boolean isAwaiting) {
        OrderItemGroup itemGroup = orderItemGroupRepository.findById(orderItemGroupId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到品項群組，ID: " + orderItemGroupId));

        itemGroup.setIsAwait(isAwaiting ? (short) 1 : (short) 0);

        orderItemGroupRepository.save(itemGroup);
    }

    @Override
    @Transactional
    public OrderDetailDto partialUpdateDispatchOrder(UUID orderId, DispatchProductOrderRequestDto requestDto) {
        Order order = orderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到要更新的訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();

        // 1. Update only specific, editable fields on the main order
        if (requestDto.getTechnicianId() != null) {
            UserAccount technician = userAccountRepository.findById(requestDto.getTechnicianId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到技師，ID: " + requestDto.getTechnicianId()));
            order.setTechnician(technician);
        } else {
            order.setTechnician(null);
        }

        // 2. Create maps for efficient lookup of existing items
        Map<UUID, OrderItem> mainItemMap = order.getItems().stream()
                .collect(Collectors.toMap(OrderItem::getOrderItemId, item -> item));
        Map<UUID, OrderItemGroup> groupItemMap = order.getItems().stream()
                .flatMap(item -> item.getItemGroups().stream())
                .collect(Collectors.toMap(OrderItemGroup::getOrderItemGroupId, group -> group));

        // 3. Iterate through DTO and update existing entities
        for (OrderItemRequestDto itemDto : requestDto.getItems()) {
            OrderItem mainItem = mainItemMap.get(itemDto.getOrderItemId());
            if (mainItem != null) {
                mainItem.setMahjongTableSerialNumber(itemDto.getMahjongTableSerialNumber());
                mainItem.setWarehouseCode(itemDto.getWarehouseCode());
            }

            if (!CollectionUtils.isEmpty(itemDto.getItemGroups())) {
                for (OrderItemGroupRequestDto groupDto : itemDto.getItemGroups()) {
                    OrderItemGroup groupItem = groupItemMap.get(groupDto.getOrderItemGroupId());
                    if (groupItem != null) {
                        groupItem.setWarehouseId(groupDto.getWarehouseId());
                        groupItem.setIsAwait(groupDto.getIsAwait() != null ? groupDto.getIsAwait() : 0);
                    }
                }
            }
        }

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, updatedOrder.getOrderStatusCode(), currentUser, "派工訂單部分更新", requestDto);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    @Transactional
    public OrderDetailDto partialUpdateWholesaleOrder(UUID orderId, WholesaleProductOrderRequestDto requestDto) {
        Order order = orderRepository.findById(orderId)
                .filter(o -> o.getIsDeleted() == 0)
                .orElseThrow(() -> new ResourceNotFoundException("找不到要更新的訂單，ID: " + orderId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short originalStatus = order.getOrderStatusCode();

        // 1. Update only specific, editable fields on the main order
        if (requestDto.getTechnicianId() != null) {
            UserAccount technician = userAccountRepository.findById(requestDto.getTechnicianId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到技師，ID: " + requestDto.getTechnicianId()));
            order.setTechnician(technician);
        } else {
            order.setTechnician(null);
        }

        // 2. Create maps for efficient lookup of existing items
        Map<UUID, OrderItem> mainItemMap = order.getItems().stream()
                .collect(Collectors.toMap(OrderItem::getOrderItemId, item -> item));
        Map<UUID, OrderItemGroup> groupItemMap = order.getItems().stream()
                .flatMap(item -> item.getItemGroups().stream())
                .collect(Collectors.toMap(OrderItemGroup::getOrderItemGroupId, group -> group));

        // 3. Iterate through DTO and update existing entities
        for (OrderItemRequestDto itemDto : requestDto.getItems()) {
            OrderItem mainItem = mainItemMap.get(itemDto.getOrderItemId());
            if (mainItem != null) {
                mainItem.setMahjongTableSerialNumber(itemDto.getMahjongTableSerialNumber());
                mainItem.setWarehouseCode(itemDto.getWarehouseCode());
            }

            if (!CollectionUtils.isEmpty(itemDto.getItemGroups())) {
                for (OrderItemGroupRequestDto groupDto : itemDto.getItemGroups()) {
                    OrderItemGroup groupItem = groupItemMap.get(groupDto.getOrderItemGroupId());
                    if (groupItem != null) {
                        groupItem.setWarehouseId(groupDto.getWarehouseId());
                        groupItem.setIsAwait(groupDto.getIsAwait() != null ? groupDto.getIsAwait() : 0);
                    }
                }
            }
        }

        Order updatedOrder = orderRepository.save(order);
        orderChangeLogService.logOrderChange(updatedOrder, originalStatus, updatedOrder.getOrderStatusCode(), currentUser, "批發訂單部分更新", requestDto);

        return convertToOrderDetailDto(updatedOrder);
    }

    @Override
    @Transactional
    public OrderDetailDto initiateCancellation(UUID orderId, String reason) {
        Order order = orderRepository.findById(orderId)
                .filter(o -> o.getIsDeleted() == 0)
                .orElseThrow(() -> new ResourceNotFoundException("找不到要取消的訂單，ID: " + orderId));

        if (!order.getOrderStatusCode().equals(OrderStatusEnum.PENDING_HQ_APPROVAL.getCode())) {
            throw new BadRequestException("只有在『訂單審核中 (總公司)』狀態的訂單才能發起取消流程。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        OrderRefund cancellation = new OrderRefund();
        cancellation.setOrderRefundId(UUID.randomUUID());
        cancellation.setOriginalOrder(order);
        cancellation.setRefundOrderNumber("C" + order.getOrderNumber());
        cancellation.setCompanyDivisionCode(order.getCompanyDivisionCode());
        cancellation.setRefundTypeCode(OrderActionTypeEnum.CANCEL.getCode());
        cancellation.setRefundStatusCode(OrderStatusEnum.CANCEL_PENDING_HQ_APPROVAL.getCode()); // -40
        cancellation.setRequestDate(OffsetDateTime.now());
        cancellation.setRequestByUser(currentUser);
        cancellation.setReason(reason);

        orderRefundRepository.save(cancellation);

        orderChangeLogService.logOrderChange(order, null, order.getOrderStatusCode(), currentUser, "發起訂單取消", reason);

        // 返回原訂單的 DTO，因為原訂單狀態不變
        return convertToOrderDetailDto(order);
    }
} 
