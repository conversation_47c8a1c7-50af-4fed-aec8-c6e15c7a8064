package com.eastking.exception;

import com.eastking.model.vo.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局異常處理器
 * 統一處理應用程序中的各種異常並返回友好的錯誤消息
 *
 * <AUTHOR> Developer
 * @date 2025/08/15
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 處理數據衝突異常 (409 Conflict)
     */
    @ExceptionHandler(DataConflictException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataConflictException(DataConflictException ex) {
        logger.warn("Data conflict: {}", ex.getMessage());
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.CONFLICT.value(), ex.getMessage()),
            HttpStatus.CONFLICT
        );
    }

    /**
     * 處理資源未找到異常 (404 Not Found)
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleResourceNotFoundException(ResourceNotFoundException ex) {
        logger.warn("Resource not found: {}", ex.getMessage());
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.NOT_FOUND.value(), ex.getMessage()),
            HttpStatus.NOT_FOUND
        );
    }

    /**
     * 處理無效數據異常 (400 Bad Request)
     */
    @ExceptionHandler(InvalidDataException.class)
    public ResponseEntity<ApiResponse<Object>> handleInvalidDataException(InvalidDataException ex) {
        logger.warn("Invalid data: {}", ex.getMessage());
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.BAD_REQUEST.value(), ex.getMessage()),
            HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 處理無效輸入異常 (400 Bad Request)
     */
    @ExceptionHandler(InvalidInputException.class)
    public ResponseEntity<ApiResponse<Object>> handleInvalidInputException(InvalidInputException ex) {
        logger.warn("Invalid input: {}", ex.getMessage());
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.BAD_REQUEST.value(), ex.getMessage()),
            HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 處理無效操作異常 (400 Bad Request)
     */
    @ExceptionHandler(InvalidOperationException.class)
    public ResponseEntity<ApiResponse<Object>> handleInvalidOperationException(InvalidOperationException ex) {
        logger.warn("Invalid operation: {}", ex.getMessage());
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.BAD_REQUEST.value(), ex.getMessage()),
            HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 處理一般錯誤請求異常 (400 Bad Request)
     */
    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadRequestException(BadRequestException ex) {
        logger.warn("Bad request: {}", ex.getMessage());
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.BAD_REQUEST.value(), ex.getMessage()),
            HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 處理方法參數驗證失敗異常 (400 Bad Request)
     * 當 @Valid 驗證失敗時觸發
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        logger.warn("Validation failed: {}", ex.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        String message = "資料驗證失敗: " + errors.toString();
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.BAD_REQUEST.value(), message),
            HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 處理數據庫完整性約束違反異常 (409 Conflict)
     * 當唯一約束、外鍵約束等被違反時觸發
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        logger.error("Data integrity violation: {}", ex.getMessage());
        
        String message = "數據完整性約束違反";
        
        // 嘗試解析具體的約束違反類型
        Throwable rootCause = ex.getRootCause();
        if (rootCause instanceof SQLException) {
            SQLException sqlEx = (SQLException) rootCause;
            String sqlMessage = sqlEx.getMessage().toLowerCase();
            
            if (sqlMessage.contains("unique") || sqlMessage.contains("duplicate")) {
                if (sqlMessage.contains("product_barcode")) {
                    message = "商品條碼已存在，請使用不同的條碼";
                } else {
                    message = "數據重複，請檢查輸入的資料";
                }
            } else if (sqlMessage.contains("foreign key")) {
                message = "關聯數據不存在，請檢查相關設定";
            } else if (sqlMessage.contains("not null")) {
                message = "必填欄位不能為空";
            }
        }
        
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.CONFLICT.value(), message),
            HttpStatus.CONFLICT
        );
    }

    /**
     * 處理所有其他未捕獲的異常 (500 Internal Server Error)
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception ex) {
        logger.error("Unexpected error occurred: ", ex);
        return new ResponseEntity<>(
            ApiResponse.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "系統發生未預期的錯誤，請聯繫系統管理員"),
            HttpStatus.INTERNAL_SERVER_ERROR
        );
    }
}
