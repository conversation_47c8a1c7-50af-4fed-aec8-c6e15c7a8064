package com.eastking.controller;

import com.eastking.enums.*;
import com.eastking.model.dto.EnumValueDto;
import com.eastking.model.dto.response.PromotionChannelTypeEnumDto;
import com.eastking.model.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "00. Enums and Configs", description = "提供系統枚舉值與配置 API")
@RestController
@RequestMapping("/api/v1/enums")
@PreAuthorize("isAuthenticated()")
public class EnumController {

    @Value("${app.image.upload-max-size-kb:1024}")
    private int uploadMaxSizeKb;

    @GetMapping("/config/upload-max-size")
    @Operation(summary = "取得圖片上傳大小限制 (KB)")
    public ResponseEntity<ApiResponse<Integer>> getUploadMaxSize() {
        return ResponseEntity.ok(ApiResponse.success(uploadMaxSizeKb));
    }

    @GetMapping("/sale-units")
    @Operation(summary = "取得所有銷售單位類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getSaleUnitEnums() {
        List<EnumValueDto> saleUnits = Arrays.stream(SaleUnitEnum.values())
                .map(enumVal -> new EnumValueDto(enumVal.getCode(), enumVal.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(saleUnits));
    }

    @GetMapping("/audit-action-types")
    @Operation(summary = "取得所有操作歷程行為類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getAuditActionTypes() {
        List<EnumValueDto> values = Arrays.stream(AuditActionTypeEnum.values())
                .map(enumVal -> new EnumValueDto(enumVal.getCode(), enumVal.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/audit-data-types")
    @Operation(summary = "取得所有操作歷程資料類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getAuditDataTypes() {
        List<EnumValueDto> values = Arrays.stream(AuditDataTypeEnum.values())
                .map(enumVal -> new EnumValueDto(enumVal.getCode(), enumVal.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/currency-codes")
    @Operation(summary = "取得所有幣別代碼")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getCurrencyCodes() {
        List<EnumValueDto> values = Arrays.stream(CurrencyCodeEnum.values())
                .map(enumVal -> new EnumValueDto(enumVal.getCode(), enumVal.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/user-account-statuses")
    @Operation(summary = "取得所有使用者帳號狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getUserAccountStatuses() {
        List<EnumValueDto> values = Arrays.stream(UserAccountStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/activation-statuses")
    @Operation(summary = "取得所有啟用狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getActivationStatuses() {
        List<EnumValueDto> values = Arrays.stream(ActivationStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/permission-statuses")
    @Operation(summary = "取得所有權限狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getPermissionStatuses() {
        List<EnumValueDto> values = Arrays.stream(PermissionStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/function-types")
    @Operation(summary = "取得所有系統功能類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getFunctionTypes() {
        List<EnumValueDto> values = Arrays.stream(FunctionTypeEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/field-permission-levels")
    @Operation(summary = "取得所有欄位權限等級")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getFieldPermissionLevels() {
        List<EnumValueDto> values = Arrays.stream(FieldPermissionLevelEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/delete-statuses")
    @Operation(summary = "取得所有刪除狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getDeleteStatuses() {
        List<EnumValueDto> values = Arrays.stream(DeleteStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/user-account-types")
    @Operation(summary = "取得所有使用者帳號類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getUserAccountTypes() {
        List<EnumValueDto> values = Arrays.stream(UserAccountTypeEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/announcement-categories")
    @Operation(summary = "取得所有公告分類")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getAnnouncementCategories() {
        List<EnumValueDto> values = Arrays.stream(AnnouncementCategoryEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/announcement-target-types")
    @Operation(summary = "取得所有公告對象類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getAnnouncementTargetTypes() {
        List<EnumValueDto> values = Arrays.stream(AnnouncementTargetTypeEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/store-statuses")
    @Operation(summary = "取得所有門市狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getStoreStatuses() {
        List<EnumValueDto> values = Arrays.stream(StoreStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/store-staff-types")
    @Operation(summary = "取得所有門市人員類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getStoreStaffTypes() {
        List<EnumValueDto> values = Arrays.stream(StoreStaffTypeEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/product-menu-types")
    @Operation(summary = "取得所有商品選單類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getProductMenuTypes() {
        List<EnumValueDto> values = Arrays.stream(ProductMenuTypeEnum.values())
                .map(enumVal -> new EnumValueDto(enumVal.name(), enumVal.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/promotion-channel-types")
    @Operation(summary = "取得所有促銷管道類型")
    public ResponseEntity<ApiResponse<List<PromotionChannelTypeEnumDto>>> getPromotionChannelTypes() {
        List<PromotionChannelTypeEnumDto> values = Arrays.stream(PromotionChannelTypeEnum.values())
                .map(enumVal -> new PromotionChannelTypeEnumDto(
                        enumVal.getCode(), 
                        enumVal.getDescription(),
                        enumVal.isAllowTargetSelection(),
                        enumVal.getTargetPlaceholder()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/store-transfer-order-statuses")
    @Operation(summary = "取得所有門市調撥單狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getStoreTransferOrderStatuses() {
        List<EnumValueDto> values = Arrays.stream(StoreTransferOrderStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/store-purchase-order-statuses")
    @Operation(summary = "取得所有門市進貨單狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getStorePurchaseOrderStatuses() {
        List<EnumValueDto> values = Arrays.stream(StorePurchaseOrderStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/erp-company-divisions")
    @Operation(summary = "取得所有ERP公司別")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getErpCompanyDivisions() {
        List<EnumValueDto> values = Arrays.stream(ErpCompanyDivisionEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/invoice-types")
    @Operation(summary = "取得所有發票類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getInvoiceTypes() {
        List<EnumValueDto> values = Arrays.stream(InvoiceTypeEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/payment-methods")
    @Operation(summary = "取得所有付款方式")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getPaymentMethods() {
        List<EnumValueDto> values = Arrays.stream(PaymentMethodEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }
    
    @GetMapping("/require-dispatch-types")
    @Operation(summary = "取得所有派工類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getRequireDispatchTypes() {
        List<EnumValueDto> values = Arrays.stream(RequireDispatchEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/order-item-types")
    @Operation(summary = "取得所有訂單品項類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getOrderItemTypes() {
        List<EnumValueDto> values = Arrays.stream(OrderItemTypeEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/order-statuses")
    @Operation(summary = "取得所有訂單狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getOrderStatuses() {
        List<EnumValueDto> values = Arrays.stream(OrderStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/order-types")
    @Operation(summary = "取得所有訂單類型")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getOrderTypes() {
        List<EnumValueDto> values = Arrays.stream(OrderTypeEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/payment-statuses")
    @Operation(summary = "取得所有付款狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getPaymentStatuses() {
        List<EnumValueDto> values = Arrays.stream(PaymentStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/dispatch-statuses")
    @Operation(summary = "取得所有派工單狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getDispatchStatuses() {
        return ResponseEntity.ok(ApiResponse.success(toDtoList(DispatchStatusEnum.values())));
    }

    @GetMapping("/dispatch-order-types")
    @Operation(summary = "取得所有派工單類型 (兼容舊版)")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getDispatchOrderTypes() {
        // Return all types from the new unified enum
        List<EnumValueDto> allTypes = Arrays.stream(DispatchRepairTypeEnum.values())
                .map(e -> new EnumValueDto(String.valueOf(e.getCode()), e.getDescription()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(allTypes));
    }

    @GetMapping("/material-order-statuses")
    @Operation(summary = "取得所有領料單狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getMaterialOrderStatuses() {
        List<EnumValueDto> values = Arrays.stream(MaterialOrderStatusEnum.values())
                .filter(enumVal -> enumVal.getCode() > 0)
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/material-return-order-statuses")
    @Operation(summary = "取得所有退料單狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getMaterialReturnOrderStatuses() {
        List<EnumValueDto> values = Arrays.stream(MaterialOrderStatusEnum.values())
                .filter(enumVal -> enumVal.getCode() < 0)
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/shipment-methods")
    @Operation(summary = "取得所有出貨方式")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getShipmentMethods() {
        List<EnumValueDto> values = Arrays.stream(ShipmentMethodEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/inventory-approval-statuses")
    @Operation(summary = "取得所有盤點審核狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getInventoryApprovalStatuses() {
        List<EnumValueDto> values = Arrays.stream(InventoryCountApprovalStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/marital-statuses")
    @Operation(summary = "取得所有婚姻狀況")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getMaritalStatuses() {
        List<EnumValueDto> values = Arrays.stream(MaritalStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/occupations")
    @Operation(summary = "取得所有職業類別")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getOccupations() {
        List<EnumValueDto> values = Arrays.stream(OccupationEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/sales-order-statuses")
    @Operation(summary = "取得所有銷售單狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getSalesOrderStatuses() {
        List<EnumValueDto> values = Arrays.stream(SalesOrderStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

    @GetMapping("/discrepancy-statuses")
    @Operation(summary = "取得所有盤點差異狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getDiscrepancyStatuses() {
        List<EnumValueDto> values = Arrays.stream(DiscrepancyStatusEnum.values())
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(values));
    }

//    @GetMapping("/return-order-statuses")
//    @Operation(summary = "取得所有退機單狀態")
//    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getReturnOrderStatuses() {
//        return ResponseEntity.ok(ApiResponse.success(toDtoList(ReturnOrderStatusEnum.values())));
//    }

    @GetMapping("/repair-order-types")
    @Operation(summary = "取得所有維修單品項類型 (已擴充為所有派工維修品項類型)")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getRepairOrderTypes() {
        // Now returns all item types from the unified enum
        return ResponseEntity.ok(ApiResponse.success(toDtoList(DispatchRepairItemTypeEnum.values())));
    }

    @GetMapping("/warranty-statuses")
    @Operation(summary = "取得所有保固狀態")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getWarrantyStatuses() {
        return ResponseEntity.ok(ApiResponse.success(toDtoList(WarrantyStatusEnum.values())));
    }

    @GetMapping("/warranty-periods")
    @Operation(summary = "取得所有保固期間選項")
    public ResponseEntity<ApiResponse<List<EnumValueDto>>> getWarrantyPeriods() {
        return ResponseEntity.ok(ApiResponse.success(toDtoList(WarrantyPeriodEnum.values())));
    }

    private List<EnumValueDto> toDtoList(BaseEnum[] enums) {
        return Arrays.stream(enums)
                .map(enumVal -> new EnumValueDto(String.valueOf(enumVal.getCode()), enumVal.getDescription(), enumVal.getCode()))
                .collect(Collectors.toList());
    }
} 