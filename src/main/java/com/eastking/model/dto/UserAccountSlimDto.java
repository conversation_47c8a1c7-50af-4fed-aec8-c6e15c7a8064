package com.eastking.model.dto;

import com.eastking.model.entity.UserAccount;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import java.util.UUID;

/**
 * Slim DTO for User Account, primarily for lists within other DTOs.
 */
@Schema(description = "使用者帳號簡要資訊 (用於列表或嵌入其他物件中)")
@Data
@Builder
public class UserAccountSlimDto {
    @Schema(description = "使用者帳號ID")
    private UUID userAccountId;
    @Schema(description = "員工編號")
    private String employeeId;
    @Schema(description = "使用者姓名")
    private String userName;

    public static UserAccountSlimDto fromEntity(UserAccount userAccount) {
        if (userAccount == null) {
            return null;
        }
        return UserAccountSlimDto.builder()
                .userAccountId(userAccount.getUserAccountId())
                .employeeId(userAccount.getEmployeeId())
                .userName(userAccount.getUserName())
                .build();
    }
} 