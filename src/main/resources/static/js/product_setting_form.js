document.addEventListener('DOMContentLoaded', function () {
    const API_BASE_URL = '/api/v1/product-settings';
    const ROLES_API_URL = '/api/v1/role-permissions/list';
    const SALE_UNITS_API_URL = '/api/v1/enums/sale-units';
    const CURRENCY_CODES_API_URL = '/api/v1/enums/currency-codes';
    const WARRANTY_PERIODS_API_URL = '/api/v1/enums/warranty-periods';

    const form = document.getElementById('product-setting-form');
    const formModeBreadcrumb = document.getElementById('form-mode-breadcrumb');
    const formTitle = document.getElementById('form-title');
    const productSettingIdField = document.getElementById('productSettingId');
    const currencyField = document.getElementById('currency');
    const addDiscountBtn = document.getElementById('add-discount-btn');
    const discountsContainer = document.getElementById('discounts-container');
    const noDiscountsMessage = document.getElementById('no-discounts-message');
    const cancelBtn = document.getElementById('cancel-btn');

    // Get company context
    const companyContext = localStorage.getItem('selectedCompanyContext');

    let availableRoles = [];
    let availableSaleUnits = [];
    let availableCurrencyCodes = [];
    let availableWarrantyPeriods = [];

    const urlParams = new URLSearchParams(window.location.search);
    const settingId = urlParams.get('id');
    const mode = urlParams.get('mode') || (settingId ? 'edit' : 'add');

    async function initializeForm() {
        await fetchRoles();
        await fetchSaleUnits();
        await fetchCurrencyCodes();
        await fetchWarrantyPeriods();
        populateSaleUnitDropdown();
        populateCurrencyDropdown();
        populateWarrantyDropdown();
        togglePriceSectionsVisibility();
        hideSectionsAsRequired();

        if (mode === 'edit' && settingId) {
            formModeBreadcrumb.textContent = '編輯設定';
            formTitle.textContent = '編輯商品設定';
            loadSettingForEdit(settingId);
        } else {
            formModeBreadcrumb.textContent = '新增設定';
            formTitle.textContent = '新增商品設定';
            updateNoDiscountsMessage();
        }
    }

    async function fetchRoles() {
        try {
            const response = await fetch(ROLES_API_URL, { headers: { 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` } });
            if (!response.ok) throw new Error('Failed to fetch roles');
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                availableRoles = apiResponse.data;
            } else {
                showToast('無法載入角色列表', 'error');
            }
        } catch (error) {
            console.error('Error fetching roles:', error);
            showToast('載入角色列表失敗', 'error');
        }
    }
    
    async function fetchSaleUnits() {
        try {
            const response = await fetch(SALE_UNITS_API_URL, { headers: { 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` } });
            if (!response.ok) throw new Error('Failed to fetch sale units');
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                availableSaleUnits = apiResponse.data;
            } else {
                showToast('無法載入銷售單位: ' + (apiResponse.message || 'Server error'), 'error');
            }
        } catch (error) {
            console.error('Error fetching sale units:', error);
            showToast('載入銷售單位失敗: ' + error.message, 'error');
        }
    }

    async function fetchCurrencyCodes() {
        if (!currencyField) return;
        console.log("PRODUCT_SETTING_FORM.JS: Fetching currency codes.");
        try {
            const response = await fetch(CURRENCY_CODES_API_URL, { headers: { 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` } });
            if (!response.ok) throw new Error('Failed to fetch currency codes');
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                availableCurrencyCodes = apiResponse.data;
                console.log("PRODUCT_SETTING_FORM.JS: Currency codes fetched:", availableCurrencyCodes);
            } else {
                showToast('無法載入幣別: ' + (apiResponse.message || 'Server error'), 'error');
            }
        } catch (error) {
            console.error('Error fetching currency codes:', error);
            showToast('載入幣別失敗: ' + error.message, 'error');
        }
    }

    async function fetchWarrantyPeriods() {
        try {
            const response = await fetch(WARRANTY_PERIODS_API_URL, { headers: { 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` } });
            if (!response.ok) throw new Error('Failed to fetch warranty periods');
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                availableWarrantyPeriods = apiResponse.data;
            } else {
                showToast('無法載入保固選項: ' + (apiResponse.message || 'Server error'), 'error');
            }
        } catch (error) {
            console.error('Error fetching warranty periods:', error);
            showToast('載入保固選項失敗: ' + error.message, 'error');
        }
    }

    function populateSaleUnitDropdown() {
        const saleUnitSelect = document.getElementById('saleUnit');
        saleUnitSelect.innerHTML = '<option value="">請選擇銷售單位...</option>';
        availableSaleUnits.forEach(unit => {
            const option = document.createElement('option');
            option.value = unit.code;
            option.textContent = unit.description;
            saleUnitSelect.appendChild(option);
        });
    }

    function populateCurrencyDropdown() {
        if (!currencyField || !availableCurrencyCodes) return;
        console.log("PRODUCT_SETTING_FORM.JS: Populating currency dropdown.");
        const currentValue = currencyField.value;
        currencyField.innerHTML = '<option value="">請選擇幣值...</option>';
        availableCurrencyCodes.forEach(currency => {
            const option = document.createElement('option');
            option.value = currency.code;
            option.textContent = `${currency.description} (${currency.code})`;
            currencyField.appendChild(option);
        });
        if (currentValue) {
            currencyField.value = currentValue;
        } else if (mode === 'add' && availableCurrencyCodes.find(c => c.code === 'TWD')) {
            currencyField.value = 'TWD';
        }
    }

    function populateWarrantyDropdown() {
        const warrantySelect = document.getElementById('warrantyMonths');
        if (!warrantySelect) return;
        warrantySelect.innerHTML = '<option value="">請選擇保固期間...</option>';
        availableWarrantyPeriods.forEach(period => {
            const option = document.createElement('option');
            option.value = period.value; // Use 'value' from EnumValueDto
            option.textContent = period.label; // Use 'label' from EnumValueDto
            warrantySelect.appendChild(option);
        });
    }

    async function loadSettingForEdit(id) {
        try {
            const response = await fetch(`${API_BASE_URL}/${id}`, { headers: { 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` } });
            if (!response.ok) {
                 if (response.status === 401 || response.status === 403) {
                    showToast('權限不足或登入逾時，請重新登入。', 'error');
                    localStorage.removeItem('jwtToken');
                    window.location.href = 'login.html';
                    return;
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                populateForm(apiResponse.data);
            } else {
                showToast(apiResponse.message || '無法載入商品設定資料', 'error');
            }
        } catch (error) {
            console.error('Error loading product setting:', error);
            showToast('載入商品設定資料失敗', 'error');
        }
    }

    function populateForm(data) {
        productSettingIdField.value = data.productSettingId;
        document.getElementById('productBarcode').value = data.productBarcode || '';
        document.getElementById('productName').value = data.productName || '';
        document.getElementById('productShortName').value = data.productShortName || '';
        document.getElementById('saleUnit').value = data.saleUnit || '';
        document.getElementById('warrantyMonths').value = data.warrantyMonths !== null ? data.warrantyMonths : '';
        
        // EastKing Prices
        if (companyContext === 'EASTKING' || data.listPrice !== null) {
            document.getElementById('listPrice').value = data.listPrice !== null ? data.listPrice : '';
            document.getElementById('salePrice').value = data.salePrice !== null ? data.salePrice : '';
            document.getElementById('costPrice').value = data.costPrice !== null ? data.costPrice : '';
            document.getElementById('saleEffectiveStartDate').value = data.saleEffectiveStartDate || '';
            document.getElementById('saleEffectiveEndDate').value = data.saleEffectiveEndDate || '';
        }
        
        // ERP Fields (Read-only)
        document.getElementById('erpCompanyDivisionDescription').value = data.erpCompanyDivisionDescription || '';
        document.getElementById('erpProductCode').value = data.erpProductCode || '';
        document.getElementById('erpProductCategory').value = data.erpProductCategory || '';
        document.getElementById('erpEastkingPrice').value = data.erpEastkingPrice !== null ? data.erpEastkingPrice : '';
        document.getElementById('erpQueyouPrice').value = data.erpQueyouPrice !== null ? data.erpQueyouPrice : '';

        // QueYou Prices
        if (companyContext === 'QUEYOU' || data.queyouListPrice !== null) {
            document.getElementById('queyouListPrice').value = data.queyouListPrice !== null ? data.queyouListPrice : '';
            document.getElementById('queyouSalePrice').value = data.queyouSalePrice !== null ? data.queyouSalePrice : '';
            document.getElementById('queyouCostPrice').value = data.queyouCostPrice !== null ? data.queyouCostPrice : '';
            document.getElementById('queyouSaleEffectiveStartDate').value = data.queyouSaleEffectiveStartDate ? data.queyouSaleEffectiveStartDate.substring(0, 16) : '';
            document.getElementById('queyouSaleEffectiveEndDate').value = data.queyouSaleEffectiveEndDate ? data.queyouSaleEffectiveEndDate.substring(0, 16) : '';
        }
        
        document.getElementById('isDispatchProduct').checked = data.isDispatchProduct || false;
        document.getElementById('isActive').checked = data.isActive === undefined ? true : data.isActive;
        currencyField.value = data.currency || '';

        discountsContainer.innerHTML = '';
        if (data.discounts && data.discounts.length > 0) {
            data.discounts.forEach(addDiscountRow);
        }
        updateNoDiscountsMessage();
    }

    function addDiscountRow(discount = {}) {
        const discountId = `discount-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newRow = document.createElement('div');
        newRow.classList.add('row', 'g-3', 'align-items-center', 'mb-3', 'discount-row');
        newRow.id = discountId;

        const roleSelectHtml = availableRoles.map(role => 
            `<option value="${role.roleId}" ${discount.roleId === role.roleId ? 'selected' : ''}>${role.roleName}</option>`
        ).join('');

        newRow.innerHTML = `
            <div class="col-md-5">
                <label for="discountRole-${discountId}" class="form-label">角色</label>
                <select class="form-select discount-role" id="discountRole-${discountId}" required>
                    <option value="">請選擇角色...</option>
                    ${roleSelectHtml}
                </select>
                <div class="invalid-feedback">請選擇角色。</div>
            </div>
            <div class="col-md-5">
                <label for="discountAmount-${discountId}" class="form-label">折扣金額 (NT $)</label>
                <div class="input-group">
                    <span class="input-group-text">NT $</span>
                    <input type="number" class="form-control discount-amount" id="discountAmount-${discountId}" value="${discount.discountAmount || ''}" step="0.01" min="0" required>
                </div>
                <div class="invalid-feedback">請輸入有效的折扣金額。</div>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-danger remove-discount-btn w-100" data-row-id="${discountId}">移除</button>
            </div>
        `;
        discountsContainer.appendChild(newRow);
        updateNoDiscountsMessage();
    }

    discountsContainer.addEventListener('click', function(event) {
        if (event.target.classList.contains('remove-discount-btn')) {
            const rowId = event.target.dataset.rowId;
            document.getElementById(rowId)?.remove();
            updateNoDiscountsMessage();
        }
    });

    addDiscountBtn.addEventListener('click', () => addDiscountRow());

    function updateNoDiscountsMessage() {
        noDiscountsMessage.classList.toggle('d-none', discountsContainer.children.length > 0);
    }

    function togglePriceSectionsVisibility() {
        const eastkingPricesCard = document.getElementById('listPrice')?.closest('.card'); // Find parent card for EastKing
        const queyouPricesCard = document.getElementById('queyouListPrice')?.closest('.card'); // Find parent card for QueYou

        if (!eastkingPricesCard || !queyouPricesCard) {
            console.warn("Could not find price section cards to toggle visibility.");
            return;
        }

        if (companyContext === 'EASTKING') {
            eastkingPricesCard.classList.remove('d-none');
            queyouPricesCard.classList.add('d-none');
        } else if (companyContext === 'QUEYOU') {
            eastkingPricesCard.classList.add('d-none');
            queyouPricesCard.classList.remove('d-none');
        } else {
            // Default or no context: show EastKing, hide QueYou (or show both if that's the requirement for "ALL" products)
            // For now, matching list page: show EastKing if no/unknown context.
            eastkingPricesCard.classList.remove('d-none');
            queyouPricesCard.classList.add('d-none');
        }
    }

    function hideSectionsAsRequired() {
        // Find the parent cards of the sections to hide
        const priceCard = document.getElementById('listPrice')?.closest('.card');
        const queyouPriceCard = document.getElementById('queyouListPrice')?.closest('.card');
        const statusCard = document.getElementById('isActive')?.closest('.card');

        if (priceCard) priceCard.style.display = 'none';
        if (queyouPriceCard) queyouPriceCard.style.display = 'none'; // Also hide this one
        if (statusCard) statusCard.style.display = 'none';
    }

    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            showToast('請檢查表單欄位是否都已正確填寫。', 'warning');
            return;
        }
        form.classList.remove('was-validated');

        const discountData = [];
        document.querySelectorAll('.discount-row').forEach(row => {
            const roleId = row.querySelector('.discount-role').value;
            const amount = row.querySelector('.discount-amount').value;
            if (roleId && amount) {
                discountData.push({
                    roleId: roleId,
                    discountAmount: parseFloat(amount)
                });
            }
        });

        const payload = {
            productSettingId: productSettingIdField.value,
            erpProductCode: document.getElementById('erpProductCode').value,
            productBarcode: document.getElementById('productBarcode').value,
            productName: document.getElementById('productName').value,
            productShortName: document.getElementById('productShortName').value,
            currency: currencyField.value,
            saleUnit: document.getElementById('saleUnit').value,
            warrantyMonths: parseInt(document.getElementById('warrantyMonths').value, 10) || null,
            
            // EastKing Prices
            listPrice: parseFloat(document.getElementById('listPrice').value) || null,
            salePrice: parseFloat(document.getElementById('salePrice').value) || null,
            costPrice: parseFloat(document.getElementById('costPrice').value) || null,
            saleEffectiveStartDate: document.getElementById('saleEffectiveStartDate').value || null,
            saleEffectiveEndDate: document.getElementById('saleEffectiveEndDate').value || null,
            
            // QueYou Prices
            queyouListPrice: parseFloat(document.getElementById('queyouListPrice').value) || null,
            queyouSalePrice: parseFloat(document.getElementById('queyouSalePrice').value) || null,
            queyouCostPrice: parseFloat(document.getElementById('queyouCostPrice').value) || null,
            queyouSaleEffectiveStartDate: document.getElementById('queyouSaleEffectiveStartDate').value ? document.getElementById('queyouSaleEffectiveStartDate').value + ":00Z" : null, // Add seconds and Z for OffsetDateTime
            queyouSaleEffectiveEndDate: document.getElementById('queyouSaleEffectiveEndDate').value ? document.getElementById('queyouSaleEffectiveEndDate').value + ":00Z" : null, // Add seconds and Z
            
            isDispatchProduct: document.getElementById('isDispatchProduct').checked,
            isActive: true,
            discounts: discountData
        };

        const method = settingId ? 'PUT' : 'POST';
        const url = settingId ? `${API_BASE_URL}/${settingId}` : API_BASE_URL;

        const confirmMessage = settingId ? "確定要更新此商品設定嗎？" : "確定要新增此商品設定嗎？";
        if (!window.confirm(confirmMessage)) {
            return; // User cancelled
        }

        try {
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('jwtToken')}`
                },
                body: JSON.stringify(payload)
            });

            const apiResponse = await response.json();

            if (response.ok && apiResponse.code === (settingId ? 200 : 201)) {
                showToast(`商品設定已成功${settingId ? '更新' : '新增'}！`, 'success');
                window.location.href = 'product_settings.html';
            } else {
                if (response.status === 401 || response.status === 403) {
                    showToast('權限不足或登入逾時，請重新登入。', 'error');
                    localStorage.removeItem('jwtToken');
                    window.location.href = 'login.html';
                } else if (response.status === 409) {
                    // 處理數據衝突錯誤 (如商品條碼重複)
                    showToast(apiResponse.message || '數據衝突，請檢查輸入的資料', 'error');
                } else if (response.status === 400) {
                    // 處理驗證錯誤
                    showToast(apiResponse.message || '資料驗證失敗，請檢查必填欄位', 'error');
                } else {
                    // 其他錯誤
                    showToast(apiResponse.message || `儲存商品設定失敗 (${response.status})`, 'error');
                }
            }
        } catch (error) {
            console.error('Save error:', error);
            showToast('儲存商品設定時發生錯誤', 'error');
        }
    });

    cancelBtn.addEventListener('click', () => {
        window.location.href = 'product_settings.html';
    });

    initializeForm();
}); 