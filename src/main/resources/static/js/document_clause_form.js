// JavaScript for document_clause_form.html

document.addEventListener('DOMContentLoaded', function () {
    console.log('document_clause_form.js loaded');

    if (typeof window.fetchAuthenticated !== 'function') {
        console.error('Main.js or fetchAuthenticated function not loaded');
        return;
    }

    const form = document.getElementById('document-clause-form');
    const clauseIdInput = document.getElementById('documentClauseId');
    const clauseTitleInput = document.getElementById('clauseTitle');
    const clauseContentTextarea = document.getElementById('clauseContent');
    const isDefaultTrueRadio = document.getElementById('isDefaultTrue');
    const isDefaultFalseRadio = document.getElementById('isDefaultFalse');
    const startTimeInput = document.getElementById('startTime');
    const endTimeInput = document.getElementById('endTime');
    const isActiveSwitch = document.getElementById('isActive');
    const sequenceOrderInput = document.getElementById('sequenceOrder');
    const pageMainTitle = document.getElementById('page-main-title');
    const formTitleBreadcrumb = document.getElementById('form-title');

    const urlParams = new URLSearchParams(window.location.search);
    const currentClauseId = urlParams.get('id');
    const isEditMode = !!currentClauseId;

    async function initializeForm() {
        if (isEditMode) {
            pageMainTitle.textContent = '編輯表尾條文';
            formTitleBreadcrumb.textContent = '編輯表尾條文';
            await loadClauseForEditing(currentClauseId);
        } else {
            pageMainTitle.textContent = '新增表尾條文';
            formTitleBreadcrumb.textContent = '新增表尾條文';
            isActiveSwitch.checked = true; // Default to active for new clauses
            isDefaultFalseRadio.checked = true; // Default to not default
        }
    }

    async function loadClauseForEditing(id) {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/document-clauses/${id}`);
            if (!response.ok) {
                throw new Error(`Failed to load clause: ${response.statusText}`);
            }
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                const clause = apiResponse.data;
                clauseIdInput.value = clause.documentClauseId;
                clauseTitleInput.value = clause.clauseTitle;
                clauseContentTextarea.value = clause.clauseContent;
                if (clause.isDefault) {
                    isDefaultTrueRadio.checked = true;
                } else {
                    isDefaultFalseRadio.checked = true;
                }
                startTimeInput.value = clause.startTime ? clause.startTime.slice(0, 16) : '';
                endTimeInput.value = clause.endTime ? clause.endTime.slice(0, 16) : '';
                isActiveSwitch.checked = clause.isActive === null ? true : clause.isActive; // Default to true if null from backend
                sequenceOrderInput.value = clause.sequenceOrder || 0;
            } else {
                window.showToast(`載入條文資料失敗: ${apiResponse.message || '未知錯誤'}`, 'error');
            }
        } catch (error) {
            console.error('Error loading clause data:', error);
            window.showToast('載入條文資料時發生錯誤', 'error');
        }
    }

    if (form) {
        form.addEventListener('submit', async function(event) {
            event.preventDefault();
            event.stopPropagation();

            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return;
            }
            form.classList.add('was-validated');

            const clauseData = {
                clauseTitle: clauseTitleInput.value,
                clauseContent: clauseContentTextarea.value,
                isDefault: isDefaultTrueRadio.checked,
                startTime: startTimeInput.value ? new Date(startTimeInput.value).toISOString() : null,
                endTime: endTimeInput.value ? new Date(endTimeInput.value).toISOString() : null,
                isActive: isActiveSwitch.checked,
                sequenceOrder: parseInt(sequenceOrderInput.value) || 0
            };

            if (isEditMode) {
                clauseData.documentClauseId = currentClauseId;
            }

            const url = isEditMode ? `/api/v1/document-clauses/${currentClauseId}` : '/api/v1/document-clauses';
            const method = isEditMode ? 'PUT' : 'POST';

            try {
                const response = await window.fetchAuthenticated(url, {
                    method: method,
                    body: JSON.stringify(clauseData)
                });
                const apiResponse = await response.json();
                if (response.ok && (apiResponse.code === 200 || apiResponse.code === 201)) {
                    window.showToast(`表尾條文已${isEditMode ? '更新' : '新增'}成功！`, 'success');
                    window.location.href = 'document_clause_list.html';
                } else {
                    window.showToast(`操作失敗: ${apiResponse.message || response.statusText}`, 'error');
                }
            } catch (error) {
                console.error('Error saving clause:', error);
                window.showToast('儲存表尾條文時發生錯誤', 'error');
            }
        });
    }

    const cancelBtn = document.getElementById('cancelBtn');
    if(cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            window.location.href = 'document_clause_list.html';
        });
    }

    initializeForm();
}); 