<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>編輯表尾條文 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <!-- <link rel="stylesheet" href="css/document_clause_form.css"> -->
</head>
<body>
<div class="app-wrapper">
    <div id="header-placeholder"></div>
    <div class="main-container">
        <div id="sidebar-placeholder"></div>
        <main class="page-content">
            <div class="container-fluid p-4">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <li class="breadcrumb-item"><a href="document_clause_list.html">表尾條文列表</a></li>
                        <li class="breadcrumb-item active" aria-current="page" id="form-title">新增表尾條文</li>
                    </ol>
                </nav>
                
                <h2 id="page-main-title">新增表尾條文</h2>

                <form id="document-clause-form" class="mt-3 needs-validation" novalidate>
                    <input type="hidden" id="documentClauseId" name="documentClauseId">

                    <div class="card mb-3">
                        <div class="card-header">條文資訊</div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <label for="clauseTitle" class="form-label">條文標題 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="clauseTitle" name="clauseTitle" required maxlength="255">
                                    <div class="invalid-feedback">請輸入條文標題。</div>
                                </div>

                                <div class="col-md-12">
                                    <label for="clauseContent" class="form-label">條文內容 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="clauseContent" name="clauseContent" rows="5" required></textarea>
                                    <div class="invalid-feedback">請輸入條文內容。</div>
                                </div>

                                <div class="col-md-4">
                                    <label for="startTime" class="form-label">啟用開始時間</label>
                                    <input type="datetime-local" class="form-control" id="startTime" name="startTime">
                                </div>

                                <div class="col-md-4">
                                    <label for="endTime" class="form-label">啟用結束時間</label>
                                    <input type="datetime-local" class="form-control" id="endTime" name="endTime">
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="sequenceOrder" class="form-label">排序</label>
                                    <input type="number" class="form-control" id="sequenceOrder" name="sequenceOrder" value="0">
                                </div>

                                <div class="col-md-2 d-flex align-items-end">
                                    <div class="form-check form-switch mb-1">
                                        <input class="form-check-input" type="checkbox" role="switch" id="isActive" name="isActive" checked>
                                        <label class="form-check-label" for="isActive">是否啟用</label>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                     <div class="form-check">
                                        <input class="form-check-input" type="radio" name="isDefaultRadio" id="isDefaultTrue" value="true">
                                        <label class="form-check-label" for="isDefaultTrue">
                                            設為預設條文
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="isDefaultRadio" id="isDefaultFalse" value="false" checked>
                                        <label class="form-check-label" for="isDefaultFalse">
                                            非預設條文
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4 mb-5">
                        <button type="button" class="btn btn-secondary me-2" id="cancelBtn">取消返回</button>
                        <button type="submit" class="btn btn-primary" id="saveBtn">確認儲存</button>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <!-- <script src="js/document_clause_form.js"></script> -->
</body>
</html> 